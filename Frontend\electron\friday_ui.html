<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Friday_UI</title>
    <style>
        body,
html {
  margin: 0;
  padding: 0;
  background-color: #111;
  font-family: 'Consolas', 'Courier New', monospace;
}

.container,
canvas {
  max-width: 100%;
  width: 100%;
}

#centeredText {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 4rem;
  font-weight: bold;
  color: #00FF00;
  text-shadow: 0 0 20px rgba(0, 255, 0, 0.8);
  font-family: 'Consolas', monospace;
  z-index: 1000;
  pointer-events: none;
}

/* Microphone Button */
#micButton {
  position: fixed;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: rgba(0, 255, 0, 0.2);
  border: 3px solid #00FF00;
  color: #00FF00;
  font-size: 32px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 1000;
}

#micButton:hover {
  background-color: rgba(0, 255, 0, 0.4);
  border: 3px solid #00FFFF;
  transform: translateX(-50%) scale(1.1);
}

#micButton.active {
  background-color: rgba(0, 255, 0, 0.6);
  border: 3px solid #00FFFF;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

/* Status Display - Bottom Left */
#statusDisplay {
  position: fixed;
  bottom: 20px;
  left: 20px;
  padding: 10px 20px;
  background-color: rgba(0, 0, 0, 0.7);
  border: 1px solid #00AA00;
  border-radius: 5px;
  color: #00FF00;
  font-family: 'Consolas', monospace;
  font-size: 14px;
  text-align: center;
  min-width: 200px;
}

/* Emotion Display - Bottom Right */
#emotionDisplay {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 10px 20px;
  background-color: rgba(0, 0, 0, 0.7);
  border: 1px solid #00AA00;
  border-radius: 5px;
  color: #00FF00;
  font-family: 'Consolas', monospace;
  font-size: 14px;
  text-align: center;
  min-width: 200px;
}
    </style>
</head>
<body>
    <!-- Main Interface -->
    <div class="container">
      <canvas id="waveCanvas"></canvas>
    </div>

    <!-- Centered Text -->
    <div id="centeredText">Matrix</div>

    <!-- Status Display -->
    <div id="statusDisplay">Initializing...</div>

    <!-- Emotion Display -->
    <div id="emotionDisplay">Neutral</div>

    <!-- Microphone Button -->
    <div id="micButton" onclick="toggleMicrophone()">🎤</div>

<script>
    // Global variables
    let microphoneActive = false;

    // Canvas and animation setup
    const canvas = document.getElementById("waveCanvas");
    const ctx = canvas.getContext("2d");
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    const turbulenceFactor = 0.25;
    const maxAmplitude = canvas.height / 3.5;
    const baseLine = canvas.height / 2;
    const numberOfWaves = 10;
    let globalTime = 0;

    function createGradient() {
      const gradient = ctx.createLinearGradient(0, 0, canvas.width, 0);
      gradient.addColorStop(0, "rgba(255, 25, 255, 0.2)");
      gradient.addColorStop(0.5, "rgba(25, 255, 255, 0.75)");
      gradient.addColorStop(1, "rgba(255, 255, 25, 0.2)");
      return gradient;
    }

    const gradient = createGradient();

    function generateSmoothWave(dataArray, frequency = 0.1, amplitude = 64) {
      const array = new Uint8Array(100);
      for (let i = 0; i < array.length; i++) {
        array[i] = (Math.sin(i * frequency + globalTime) + 1) * amplitude;
      }

      return array;
    }

    function animateWaves(dataArray, analyser) {
      const isSpeaking = dataArray.some((value) => value > 0);
      if (isSpeaking) {
        // Speaking
        analyser.getByteFrequencyData(dataArray);
      } else {
        // Thinking Mode
        dataArray = generateSmoothWave(dataArray, 0.05, 16);
      }
      drawWave(dataArray, analyser);
    }

    navigator.mediaDevices
      .getUserMedia({ audio: true, video: false })
      .then((stream) => {
        const audioContext = new (window.AudioContext ||
          window.webkitAudioContext)();
        const analyser = audioContext.createAnalyser();
        const microphone = audioContext.createMediaStreamSource(stream);
        microphone.connect(analyser);
        const dataArray = new Uint8Array(analyser.frequencyBinCount);
        const waves = dataArray.slice(0, 250);
        animateWaves(waves, analyser);
      })
      .catch((error) => {
        console.error("Access to microphone denied", error);
      });

    function drawWave(dataArray, analyser) {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      globalTime += 0.05;

      for (let j = 0; j < numberOfWaves; j++) {
        ctx.beginPath();
        ctx.lineWidth = 2;
        ctx.strokeStyle = gradient;

        let x = 0;
        let sliceWidth = (canvas.width * 1.0) / dataArray.length;
        let lastX = 0;
        let lastY = baseLine;

        for (let i = 0; i < dataArray.length; i++) {
          const v = dataArray[i] / 96.0;
          const mid = dataArray.length / 2;
          const distanceFromMid = Math.abs(i - mid) / mid;
          const dampFactor = 1 - Math.pow((2 * i) / dataArray.length - 1, 2);

          const amplitude = maxAmplitude * dampFactor * (1 - distanceFromMid);
          const isWaveInverted = j % 2 ? 1 : -1;
          const frequency = isWaveInverted * (0.05 + turbulenceFactor);

          const y =
            baseLine + Math.sin(i * frequency + globalTime + j) * amplitude * v;

          if (i === 0) {
            ctx.moveTo(x, y);
          } else {
            let xc = (x + lastX) / 2;
            let yc = (y + lastY) / 2;
            ctx.quadraticCurveTo(lastX, lastY, xc, yc);
          }

          lastX = x;
          lastY = y;
          x += sliceWidth;
        }

        ctx.lineTo(canvas.width, lastY);
        ctx.stroke();
      }

      requestAnimationFrame(() => animateWaves(dataArray, analyser));
    }



    // Microphone toggle function
    function toggleMicrophone() {
        microphoneActive = !microphoneActive;
        const micButton = document.getElementById('micButton');

        if (microphoneActive) {
            micButton.classList.add('active');
            micButton.innerHTML = '🎤';
            // Write to Mic.data file
            writeMicrophoneStatus('True');
            console.log('Microphone activated');
        } else {
            micButton.classList.remove('active');
            micButton.innerHTML = '🔇';
            // Write to Mic.data file
            writeMicrophoneStatus('False');
            console.log('Microphone deactivated');
        }
    }

    // File I/O functions (using Electron's file system access)
    function writeMicrophoneStatus(status) {
        // This will be handled by Electron's main process
        if (window.electronAPI) {
            window.electronAPI.writeMicStatus(status);
        }
    }



    function displayStatus() {
        // This will be handled by Electron's main process
        if (window.electronAPI) {
            window.electronAPI.readStatusFile().then(data => {
                const statusDiv = document.getElementById('statusDisplay');
                if (data && data.trim()) {
                    statusDiv.innerText = data;
                }
            }).catch(error => {
                console.error('Error fetching status:', error);
            });
        }
    }

    function displayEmotion() {
        // This will be handled by Electron's main process
        if (window.electronAPI) {
            window.electronAPI.readEmotionFile().then(data => {
                const emotionDiv = document.getElementById('emotionDisplay');
                if (data && data.trim()) {
                    emotionDiv.innerText = data;
                } else {
                    emotionDiv.innerText = 'Neutral';
                }
            }).catch(error => {
                console.error('Error fetching emotion:', error);
                const emotionDiv = document.getElementById('emotionDisplay');
                if (emotionDiv) {
                    emotionDiv.innerText = 'Neutral';
                }
            });
        }
    }



    // Initialize the interface
    window.addEventListener('load', function() {
        // Set initial microphone state to active
        microphoneActive = true;
        toggleMicrophone();

        // Start periodic updates
        setInterval(() => {
            displayStatus();
            displayEmotion();
        }, 1000);

        console.log('Matrix UI initialized');
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
    });
</script>
</body>
</html>