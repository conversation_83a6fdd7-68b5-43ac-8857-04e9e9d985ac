<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Friday_UI</title>
    <style>
        body,
html {
  margin: 0;
  padding: 0;
  background-color: #111;
  font-family: 'Consolas', 'Courier New', monospace;
}

.container,
canvas {
  max-width: 100%;
  width: 100%;
}

#aiResponse {
  align-items: center;
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 5px;
  font-family: Arial, sans-serif;
  color: rgb(0, 255, 255);
  font-size: 20px;
  text-align: center;
  pointer-events: none;
  box-shadow: #928b8b;
}

#centeredText {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 3em;
  border: 2px solid white;
  padding: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
}

/* Navigation */
#navigation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 50px;
  background-color: rgba(0, 17, 0, 0.9);
  border-bottom: 1px solid #00AA00;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.nav-button {
  background-color: #003300;
  color: #00FF00;
  border: 1px solid #00AA00;
  border-radius: 5px;
  padding: 8px 20px;
  margin: 0 10px;
  font-family: 'Consolas', monospace;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-button:hover {
  background-color: #005500;
  border: 1px solid #00FF00;
}

.nav-button.active {
  background-color: #007700;
  border: 2px solid #00FF00;
}

/* Chat Interface */
#chatInterface {
  position: fixed;
  top: 60px;
  left: 20px;
  right: 20px;
  bottom: 20px;
  background-color: rgba(0, 17, 0, 0.8);
  border: 2px solid #00AA00;
  border-radius: 10px;
  padding: 20px;
  display: none;
  overflow-y: auto;
}

#chatMessages {
  height: calc(100% - 100px);
  overflow-y: auto;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 5px;
  color: #00FF00;
  font-size: 14px;
  line-height: 1.6;
}

/* Microphone Button */
#micButton {
  position: fixed;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: rgba(0, 255, 0, 0.2);
  border: 3px solid #00FF00;
  color: #00FF00;
  font-size: 32px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 1000;
}

#micButton:hover {
  background-color: rgba(0, 255, 0, 0.4);
  border: 3px solid #00FFFF;
  transform: translateX(-50%) scale(1.1);
}

#micButton.active {
  background-color: rgba(0, 255, 0, 0.6);
  border: 3px solid #00FFFF;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

/* Status Display */
#statusDisplay {
  position: fixed;
  bottom: 200px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 255, 0, 0.1);
  border: 1px solid #00AA00;
  border-radius: 10px;
  padding: 15px 25px;
  color: #00FF00;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  min-width: 200px;
}
    </style>
</head>
<body>
    <!-- Navigation -->
    <div id="navigation">
        <button class="nav-button active" onclick="showHome()">HOME</button>
        <!-- <button class="nav-button" onclick="showChat()">CHAT</button> -->
    </div>

    <!-- Main Interface -->
    <div class="container">
      <canvas id="waveCanvas"></canvas>
    </div>
    
    <!-- Centered Text -->
    <!-- <div id="centeredText">FRIDAY</div> -->
    
    <!-- Status Display -->
    <div id="statusDisplay">Ready - Click microphone to activate</div>
    
    <!-- Microphone Button -->
    <div id="micButton" onclick="toggleMicrophone()">🎤</div>

<script>
    // Global variables
    let microphoneActive = false;
    let currentView = 'home';

    // Canvas and animation setup
    const canvas = document.getElementById("waveCanvas");
    const ctx = canvas.getContext("2d");
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    const turbulenceFactor = 0.25;
    const maxAmplitude = canvas.height / 3.5;
    const baseLine = canvas.height / 2;
    const numberOfWaves = 10;
    let globalTime = 0;

    function createGradient() {
      const gradient = ctx.createLinearGradient(0, 0, canvas.width, 0);
      gradient.addColorStop(0, "rgba(255, 25, 255, 0.2)");
      gradient.addColorStop(0.5, "rgba(25, 255, 255, 0.75)");
      gradient.addColorStop(1, "rgba(255, 255, 25, 0.2)");
      return gradient;
    }

    const gradient = createGradient();

    function generateSmoothWave(dataArray, frequency = 0.1, amplitude = 64) {
      const array = new Uint8Array(100);
      for (let i = 0; i < array.length; i++) {
        array[i] = (Math.sin(i * frequency + globalTime) + 1) * amplitude;
      }
      return array;
    }

    function animateWaves(dataArray, analyser) {
      const isSpeaking = dataArray.some((value) => value > 0);
      if (isSpeaking) {
        analyser.getByteFrequencyData(dataArray);
      } else {
        dataArray = generateSmoothWave(dataArray, 0.05, 16);
      }
      drawWave(dataArray, analyser);
    }

    // Initialize microphone access
    navigator.mediaDevices
      .getUserMedia({ audio: true, video: false })
      .then((stream) => {
        const audioContext = new (window.AudioContext ||
          window.webkitAudioContext)();
        const analyser = audioContext.createAnalyser();
        const microphone = audioContext.createMediaStreamSource(stream);
        microphone.connect(analyser);
        const dataArray = new Uint8Array(analyser.frequencyBinCount);
        const waves = dataArray.slice(0, 250);
        animateWaves(waves, analyser);
      })
      .catch((error) => {
        console.error("Access to microphone denied", error);
        // Use fallback animation without microphone
        const dataArray = new Uint8Array(250);
        animateWaves(dataArray, null);
      });

    function drawWave(dataArray, analyser) {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      globalTime += 0.05;

      for (let j = 0; j < numberOfWaves; j++) {
        ctx.beginPath();
        ctx.lineWidth = 2;
        ctx.strokeStyle = gradient;

        let x = 0;
        let sliceWidth = (canvas.width * 1.0) / dataArray.length;
        let lastX = 0;
        let lastY = baseLine;

        for (let i = 0; i < dataArray.length; i++) {
          const v = dataArray[i] / 96.0;
          const mid = dataArray.length / 2;
          const distanceFromMid = Math.abs(i - mid) / mid;
          const dampFactor = 1 - Math.pow((2 * i) / dataArray.length - 1, 2);

          const amplitude = maxAmplitude * dampFactor * (1 - distanceFromMid);
          const isWaveInverted = j % 2 ? 1 : -1;
          const frequency = isWaveInverted * (0.05 + turbulenceFactor);

          const y =
            baseLine + Math.sin(i * frequency + globalTime + j) * amplitude * v;

          if (i === 0) {
            ctx.moveTo(x, y);
          } else {
            let xc = (x + lastX) / 2;
            let yc = (y + lastY) / 2;
            ctx.quadraticCurveTo(lastX, lastY, xc, yc);
          }

          lastX = x;
          lastY = y;
          x += sliceWidth;
        }

        ctx.lineTo(canvas.width, lastY);
        ctx.stroke();
      }

      requestAnimationFrame(() => animateWaves(dataArray, analyser));
    }

    // Navigation functions
    function showHome() {
        currentView = 'home';
        document.getElementById('chatInterface').style.display = 'none';
        document.getElementById('centeredText').style.display = 'block';

        // Update navigation buttons
        const buttons = document.querySelectorAll('.nav-button');
        buttons.forEach(btn => btn.classList.remove('active'));
        buttons[0].classList.add('active');
    }

    function showChat() {
        currentView = 'chat';
        document.getElementById('chatInterface').style.display = 'block';
        document.getElementById('centeredText').style.display = 'none';

        // Update navigation buttons
        const buttons = document.querySelectorAll('.nav-button');
        buttons.forEach(btn => btn.classList.remove('active'));
        buttons[1].classList.add('active');
    }

    // Microphone toggle function
    function toggleMicrophone() {
        microphoneActive = !microphoneActive;
        const micButton = document.getElementById('micButton');

        if (microphoneActive) {
            micButton.classList.add('active');
            micButton.innerHTML = '🎤';
            console.log('Microphone activated');
            document.getElementById('statusDisplay').innerText = 'Listening...';
        } else {
            micButton.classList.remove('active');
            micButton.innerHTML = '🔇';
            console.log('Microphone deactivated');
            document.getElementById('statusDisplay').innerText = 'Available.....';
        }
    }

    // Simulate file reading for demo purposes
    function displayAIResponse() {
        const responses = [
            "Hello sir How can I help you today!",
            "I'm ready to assist you.",
            "Listening for your commands...",
            "Processing your request...",
            "How may I help you?"
        ];

        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
        const aiResponseDiv = document.getElementById('aiResponse');
        aiResponseDiv.innerText = randomResponse;
    }

    function displayChatMessages() {
        if (currentView === 'chat') {
            const chatDiv = document.getElementById('chatMessages');
            const sampleChat = `
User: Hello Friday, how are you?<br>
Friday: Welcome User. I am doing well. How may I help you?<br><br>
User: What's the weather like today?<br>
Friday: I can help you check the weather. Please specify your location.<br><br>
User: Tell me a joke<br>
Friday: Why don't scientists trust atoms? Because they make up everything!<br><br>
            `;
            chatDiv.innerHTML = sampleChat;
            chatDiv.scrollTop = chatDiv.scrollHeight;
        }
    }

    // Initialize the interface
    window.addEventListener('load', function() {
        console.log('Friday UI initialized');

        // Start periodic updates
        setInterval(() => {
            if (Math.random() < 0.1) { // 10% chance to update
                displayAIResponse();
            }
            if (currentView === 'chat') {
                displayChatMessages();
            }
        }, 2000);
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
    });
</script>
</body>
</html>
