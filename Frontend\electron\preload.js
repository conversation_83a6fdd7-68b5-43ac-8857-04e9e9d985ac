const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
    writeMicStatus: (status) => ipcRenderer.invoke('write-mic-status', status),
    readResponseFile: () => ipcRenderer.invoke('read-response-file'),
    readStatusFile: () => ipcRenderer.invoke('read-status-file'),
    readEmotionFile: () => ipcRenderer.invoke('read-emotion-file')
});
