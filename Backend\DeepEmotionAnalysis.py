import os
import numpy as np
import librosa
import tensorflow as tf
from transformers import Wav2Vec2Processor, Wav2Vec2Model
import torch
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
import warnings
import time
from collections import deque
import threading
import json

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

class DeepEmotionRecognizer:
    """
    Advanced Speech Emotion Recognition using Wav2Vec2 and TensorFlow.
    Implements real-time emotion detection with deep learning models.
    """
    
    def __init__(self):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"🧠 Deep Emotion Recognition initialized on: {self.device}")
        
        # Emotion mapping for 7-class system
        self.emotion_labels = [
            'anger', 'disgust', 'fear', 'happiness', 
            'pleasant_surprise', 'sadness', 'neutral'
        ]
        
        # Map to existing Matrix AI emotion system
        self.emotion_mapping = {
            'anger': 'angry',
            'disgust': 'angry',
            'fear': 'sad',
            'happiness': 'happy',
            'pleasant_surprise': 'excited',
            'sadness': 'sad',
            'neutral': 'neutral'
        }
        
        # Audio processing parameters
        self.sample_rate = 16000
        self.max_length = 3.0  # seconds
        self.hop_length = 512
        self.n_mels = 128
        self.n_fft = 2048
        
        # Model components
        self.wav2vec_processor = None
        self.wav2vec_model = None
        self.emotion_classifier = None
        self.is_initialized = False
        
        # Performance tracking
        self.prediction_history = deque(maxlen=10)
        self.confidence_threshold = 0.6
        self.smoothing_factor = 0.3
        
        # Initialize models
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize Wav2Vec2 and emotion classification models."""
        try:
            print("🔄 Loading Wav2Vec2 model...")
            # Load pre-trained Wav2Vec2 model
            self.wav2vec_processor = Wav2Vec2Processor.from_pretrained(
                "facebook/wav2vec2-base-960h"
            )
            self.wav2vec_model = Wav2Vec2Model.from_pretrained(
                "facebook/wav2vec2-base-960h"
            ).to(self.device)
            
            print("🔄 Building emotion classification model...")
            # Build TensorFlow emotion classifier
            self._build_emotion_classifier()
            
            self.is_initialized = True
            print("✅ Deep emotion recognition models loaded successfully")
            
        except Exception as e:
            print(f"❌ Error initializing models: {e}")
            print("⚠️ Falling back to basic emotion detection")
            self.is_initialized = False
    
    def _build_emotion_classifier(self):
        """Build TensorFlow emotion classification model."""
        try:
            # Define model architecture
            self.emotion_classifier = tf.keras.Sequential([
                tf.keras.layers.Dense(512, activation='relu', input_shape=(768,)),
                tf.keras.layers.Dropout(0.3),
                tf.keras.layers.Dense(256, activation='relu'),
                tf.keras.layers.Dropout(0.3),
                tf.keras.layers.Dense(128, activation='relu'),
                tf.keras.layers.Dropout(0.2),
                tf.keras.layers.Dense(len(self.emotion_labels), activation='softmax')
            ])
            
            # Compile model
            self.emotion_classifier.compile(
                optimizer='adam',
                loss='categorical_crossentropy',
                metrics=['accuracy']
            )
            
            # Try to load pre-trained weights if available
            weights_path = os.path.join("Data", "emotion_classifier_weights.h5")
            if os.path.exists(weights_path):
                self.emotion_classifier.load_weights(weights_path)
                print("✅ Loaded pre-trained emotion classifier weights")
            else:
                print("⚠️ No pre-trained weights found, using random initialization")
                # Initialize with random weights for now
                dummy_input = np.random.random((1, 768))
                _ = self.emotion_classifier.predict(dummy_input, verbose=0)
                
        except Exception as e:
            print(f"❌ Error building emotion classifier: {e}")
            raise
    
    def extract_wav2vec_features(self, audio_data: np.ndarray) -> np.ndarray:
        """Extract features using Wav2Vec2 model."""
        try:
            # Ensure audio is the right length and format
            if len(audio_data.shape) > 1:
                audio_data = audio_data.flatten()
            
            # Resample if necessary
            if len(audio_data) > self.sample_rate * self.max_length:
                audio_data = audio_data[:int(self.sample_rate * self.max_length)]
            elif len(audio_data) < self.sample_rate:
                # Pad short audio
                padding = self.sample_rate - len(audio_data)
                audio_data = np.pad(audio_data, (0, padding), mode='constant')
            
            # Process with Wav2Vec2
            inputs = self.wav2vec_processor(
                audio_data, 
                sampling_rate=self.sample_rate, 
                return_tensors="pt",
                padding=True
            ).to(self.device)
            
            with torch.no_grad():
                outputs = self.wav2vec_model(**inputs)
                # Use mean pooling over sequence dimension
                features = outputs.last_hidden_state.mean(dim=1).cpu().numpy()
            
            return features.flatten()
            
        except Exception as e:
            print(f"❌ Error extracting Wav2Vec2 features: {e}")
            # Return zero features as fallback
            return np.zeros(768)
    
    def extract_librosa_features(self, audio_data: np.ndarray) -> np.ndarray:
        """Extract additional features using librosa."""
        try:
            # Ensure audio is mono
            if len(audio_data.shape) > 1:
                audio_data = librosa.to_mono(audio_data.T)
            
            features = []
            
            # MFCC features
            mfccs = librosa.feature.mfcc(
                y=audio_data, sr=self.sample_rate, 
                n_mfcc=13, hop_length=self.hop_length
            )
            features.extend([
                np.mean(mfccs, axis=1),
                np.std(mfccs, axis=1),
                np.max(mfccs, axis=1),
                np.min(mfccs, axis=1)
            ])
            
            # Spectral features
            spectral_centroids = librosa.feature.spectral_centroid(
                y=audio_data, sr=self.sample_rate, hop_length=self.hop_length
            )
            features.extend([
                np.mean(spectral_centroids),
                np.std(spectral_centroids)
            ])
            
            # Zero crossing rate
            zcr = librosa.feature.zero_crossing_rate(
                audio_data, hop_length=self.hop_length
            )
            features.extend([
                np.mean(zcr),
                np.std(zcr)
            ])
            
            # Chroma features
            chroma = librosa.feature.chroma_stft(
                y=audio_data, sr=self.sample_rate, hop_length=self.hop_length
            )
            features.extend([
                np.mean(chroma, axis=1),
                np.std(chroma, axis=1)
            ])
            
            return np.concatenate([f.flatten() if hasattr(f, 'flatten') else [f] for f in features])
            
        except Exception as e:
            print(f"❌ Error extracting librosa features: {e}")
            return np.array([])
    
    def predict_emotion(self, audio_data: np.ndarray) -> Tuple[str, float, Dict[str, float]]:
        """
        Predict emotion from audio data using deep learning models.
        
        Returns:
            Tuple of (emotion_name, confidence, emotion_probabilities)
        """
        if not self.is_initialized:
            return "neutral", 0.5, {"neutral": 1.0}
        
        try:
            start_time = time.time()
            
            # Extract Wav2Vec2 features
            wav2vec_features = self.extract_wav2vec_features(audio_data)
            
            # Predict emotion probabilities
            features_input = wav2vec_features.reshape(1, -1)
            emotion_probs = self.emotion_classifier.predict(features_input, verbose=0)[0]
            
            # Get predicted emotion
            predicted_idx = np.argmax(emotion_probs)
            predicted_emotion = self.emotion_labels[predicted_idx]
            confidence = float(emotion_probs[predicted_idx])
            
            # Create probability dictionary
            prob_dict = {
                emotion: float(prob) 
                for emotion, prob in zip(self.emotion_labels, emotion_probs)
            }
            
            # Map to Matrix AI emotion system
            matrix_emotion = self.emotion_mapping.get(predicted_emotion, 'neutral')
            
            # Apply smoothing with prediction history
            smoothed_emotion, smoothed_confidence = self._apply_smoothing(
                matrix_emotion, confidence
            )
            
            processing_time = time.time() - start_time
            
            # Log prediction if confidence is high enough
            if smoothed_confidence > self.confidence_threshold:
                print(f"🎭 Deep Emotion: {smoothed_emotion} ({smoothed_confidence:.2f}) "
                      f"[{processing_time:.3f}s]")
            
            return smoothed_emotion, smoothed_confidence, prob_dict
            
        except Exception as e:
            print(f"❌ Error in emotion prediction: {e}")
            return "neutral", 0.5, {"neutral": 1.0}
    
    def _apply_smoothing(self, emotion: str, confidence: float) -> Tuple[str, float]:
        """Apply temporal smoothing to emotion predictions."""
        try:
            # Add current prediction to history
            self.prediction_history.append({
                'emotion': emotion,
                'confidence': confidence,
                'timestamp': time.time()
            })
            
            if len(self.prediction_history) < 3:
                return emotion, confidence
            
            # Calculate weighted average of recent predictions
            recent_predictions = list(self.prediction_history)[-3:]
            
            # Weight by confidence and recency
            total_weight = 0
            emotion_weights = {}
            
            for i, pred in enumerate(recent_predictions):
                # More recent predictions get higher weight
                time_weight = (i + 1) / len(recent_predictions)
                weight = pred['confidence'] * time_weight
                
                if pred['emotion'] not in emotion_weights:
                    emotion_weights[pred['emotion']] = 0
                emotion_weights[pred['emotion']] += weight
                total_weight += weight
            
            if total_weight > 0:
                # Normalize weights
                for emotion_key in emotion_weights:
                    emotion_weights[emotion_key] /= total_weight
                
                # Get most confident emotion
                best_emotion = max(emotion_weights, key=emotion_weights.get)
                best_confidence = emotion_weights[best_emotion]
                
                # Apply smoothing factor
                if best_emotion == emotion:
                    smoothed_confidence = (
                        self.smoothing_factor * confidence + 
                        (1 - self.smoothing_factor) * best_confidence
                    )
                else:
                    smoothed_confidence = best_confidence
                
                return best_emotion, smoothed_confidence
            
            return emotion, confidence
            
        except Exception as e:
            print(f"❌ Error in smoothing: {e}")
            return emotion, confidence
    
    def get_model_info(self) -> Dict[str, any]:
        """Get information about the loaded models."""
        return {
            "initialized": self.is_initialized,
            "device": str(self.device),
            "emotion_labels": self.emotion_labels,
            "emotion_mapping": self.emotion_mapping,
            "sample_rate": self.sample_rate,
            "max_length": self.max_length,
            "confidence_threshold": self.confidence_threshold,
            "prediction_history_size": len(self.prediction_history)
        }
    
    def reset_history(self):
        """Reset prediction history."""
        self.prediction_history.clear()
        print("🔄 Emotion prediction history reset")

# Global instance
deep_emotion_recognizer = DeepEmotionRecognizer()

# Main interface functions
def analyze_deep_emotion(audio_data: np.ndarray, sample_rate: int = 16000) -> Tuple[str, float]:
    """
    Main function for deep learning emotion analysis.
    
    Args:
        audio_data: Audio data as numpy array
        sample_rate: Sample rate of the audio
    
    Returns:
        Tuple of (emotion_name, confidence_score)
    """
    if sample_rate != deep_emotion_recognizer.sample_rate:
        # Resample audio if necessary
        audio_data = librosa.resample(
            audio_data, orig_sr=sample_rate, 
            target_sr=deep_emotion_recognizer.sample_rate
        )
    
    emotion, confidence, _ = deep_emotion_recognizer.predict_emotion(audio_data)
    return emotion, confidence

def get_deep_emotion_probabilities(audio_data: np.ndarray, sample_rate: int = 16000) -> Dict[str, float]:
    """Get detailed emotion probabilities from deep learning model."""
    if sample_rate != deep_emotion_recognizer.sample_rate:
        audio_data = librosa.resample(
            audio_data, orig_sr=sample_rate, 
            target_sr=deep_emotion_recognizer.sample_rate
        )
    
    _, _, probabilities = deep_emotion_recognizer.predict_emotion(audio_data)
    return probabilities

def is_deep_emotion_available() -> bool:
    """Check if deep emotion recognition is available."""
    return deep_emotion_recognizer.is_initialized

def get_deep_emotion_info() -> Dict[str, any]:
    """Get deep emotion recognition system information."""
    return deep_emotion_recognizer.get_model_info()

def reset_deep_emotion_history():
    """Reset deep emotion prediction history."""
    deep_emotion_recognizer.reset_history()

if __name__ == "__main__":
    # Test the deep emotion recognition system
    print("🧪 Testing Deep Emotion Recognition System...")
    
    # Generate test audio
    duration = 2.0
    sample_rate = 16000
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # Test different audio patterns
    test_cases = [
        ("High energy audio", np.sin(2 * np.pi * 440 * t) * 0.8),
        ("Low energy audio", np.sin(2 * np.pi * 220 * t) * 0.2),
        ("Variable audio", np.sin(2 * np.pi * 330 * t) * np.random.random(len(t)) * 0.6),
    ]
    
    for name, audio in test_cases:
        emotion, confidence = analyze_deep_emotion(audio, sample_rate)
        probabilities = get_deep_emotion_probabilities(audio, sample_rate)
        print(f"{name}: {emotion} (confidence: {confidence:.2f})")
        print(f"  Probabilities: {probabilities}")
    
    print(f"\nSystem info: {get_deep_emotion_info()}")
