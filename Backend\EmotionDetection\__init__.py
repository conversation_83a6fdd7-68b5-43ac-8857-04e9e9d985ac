"""
Enhanced Emotion Detection System
Combines ML-based emotion recognition with real-time processing for Matrix AI
"""

from .emotion_detector import (
    EmotionDetector,
    emotion_detector,
    get_current_emotion,
    get_emotion_context,
    analyze_audio_emotion,
    add_emotion_record
)

from .emotion_bridge import (
    EnhancedEmotionBridge,
    enhanced_emotion_bridge,
    start_emotion_bridge,
    stop_emotion_bridge,
    get_emotion_bridge_status,
    force_emotion_update
)

from .utilities import (
    extract_feature,
    AVAILABLE_EMOTIONS,
    int2emotion
)

__version__ = "1.0.0"
__author__ = "Matrix AI Team"

# Convenience functions for easy integration
def initialize_emotion_system():
    """Initialize the complete emotion detection system."""
    print("🎭 Initializing Enhanced Emotion Detection System...")
    
    # The emotion detector is automatically initialized when imported
    # The emotion bridge needs to be started manually
    
    print("✅ Enhanced Emotion Detection System ready")
    return True

def get_emotion_summary():
    """Get a comprehensive emotion summary."""
    current = get_current_emotion()
    context = get_emotion_context()
    
    return {
        "current": current,
        "context": context,
        "system_version": __version__,
        "last_updated": current.get("timestamp", 0)
    }

# Export main components
__all__ = [
    # Main classes
    'EmotionDetector',
    'EnhancedEmotionBridge',
    
    # Global instances
    'emotion_detector',
    'enhanced_emotion_bridge',
    
    # Core functions
    'get_current_emotion',
    'get_emotion_context',
    'analyze_audio_emotion',
    'add_emotion_record',
    
    # Bridge functions
    'start_emotion_bridge',
    'stop_emotion_bridge',
    'get_emotion_bridge_status',
    'force_emotion_update',
    
    # Utilities
    'extract_feature',
    'AVAILABLE_EMOTIONS',
    'int2emotion',
    
    # System functions
    'initialize_emotion_system',
    'get_emotion_summary'
]
