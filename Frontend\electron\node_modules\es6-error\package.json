{"name": "es6-error", "version": "4.1.1", "description": "Easily-extendable error for use with ES6 classes", "main": "./lib/index", "module": "./es6/index.js", "typings": "./typings/index.d.ts", "files": ["lib", "es6", "typings"], "scripts": {"test": "cross-env BABEL_ENV=test mocha --require babel-core/register --recursive", "clean": "rimraf lib es6", "build": "npm run clean && npm run build:cjs && npm run build:es6", "build:cjs": "mkdir -p lib && cross-env BABEL_ENV=cjs babel src/index.js -o lib/index.js", "build:es6": "mkdir -p es6 && cross-env BABEL_ENV=es6 babel src/index.js -o es6/index.js", "prepublishOnly": "npm run build && npm run test"}, "repository": {"type": "git", "url": "https://github.com/bjyoungblood/es6-error.git"}, "keywords": ["es6", "error", "babel"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/bjyoungblood/es6-error/issues"}, "homepage": "https://github.com/bjyoungblood/es6-error", "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-builtin-extend": "^1.1.2", "babel-preset-env": "^1.6.1", "chai": "^4.1.2", "cross-env": "^5.1.1", "mocha": "^4.0.1", "rimraf": "^2.6.2"}, "dependencies": {}}