"""
Real-time Emotion Detection System
Integrates the ML-based emotion recognition with real-time audio processing
"""

import os
import json
import time
import numpy as np
import threading
from typing import Dict, List, Optional, Tuple
from collections import deque
import tempfile
import soundfile

# Import the feature extraction from the new system
from .utilities import extract_feature, AVAILABLE_EMOTIONS

# Try to import ML libraries
try:
    from sklearn.neural_network import MLPClassifier
    from sklearn.svm import SVC
    from sklearn.ensemble import RandomForestClassifier
    import joblib
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False
    print("⚠️ ML libraries not available. Using rule-based emotion detection.")

class EmotionDetector:
    """
    Real-time emotion detection system that combines ML-based audio analysis
    with context management for the Matrix AI assistant.
    """
    
    def __init__(self):
        self.data_dir = os.path.join(os.getcwd(), "Data")
        self.temp_dir = os.path.join(os.getcwd(), "Frontend", "Files")
        self.model_dir = os.path.join(os.getcwd(), "Backend", "EmotionDetection", "models")
        
        # Ensure directories exist
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)
        os.makedirs(self.model_dir, exist_ok=True)
        
        # File paths
        self.emotion_file = os.path.join(self.data_dir, "EmotionData.json")
        self.context_file = os.path.join(self.temp_dir, "EmotionContext.json")
        self.history_file = os.path.join(self.data_dir, "EmotionHistory.json")
        self.gui_emotion_file = os.path.join(self.temp_dir, "Emotion.data")
        
        # Emotion history
        self.emotion_history = deque(maxlen=50)
        self.load_emotion_history()
        
        # ML Model
        self.model = None
        self.model_type = "rule_based"
        self.load_or_create_model()
        
        # Real-time monitoring
        self.is_monitoring = False
        self.monitor_thread = None
        self.last_emotion = "neutral"
        self.last_update_time = 0
        
        print("✅ Enhanced Emotion Detection System initialized")
    
    def load_or_create_model(self):
        """Load a pre-trained model or create a simple rule-based classifier."""
        model_path = os.path.join(self.model_dir, "emotion_model.pkl")
        
        if ML_AVAILABLE and os.path.exists(model_path):
            try:
                self.model = joblib.load(model_path)
                self.model_type = "ml_model"
                print("✅ Loaded pre-trained emotion detection model")
                return
            except Exception as e:
                print(f"⚠️ Error loading model: {e}")
        
        # Create a simple rule-based model if ML model not available
        self.create_rule_based_model()
    
    def create_rule_based_model(self):
        """Create a simple rule-based emotion classifier."""
        self.model_type = "rule_based"
        print("📝 Using rule-based emotion detection")
    
    def extract_audio_features(self, audio_file_path: str) -> Optional[np.ndarray]:
        """Extract features from audio file using the new system's utilities."""
        try:
            if not os.path.exists(audio_file_path):
                return None
            
            # Use the new system's feature extraction
            features = extract_feature(
                audio_file_path,
                mfcc=True,
                chroma=True,
                mel=True,
                contrast=True,
                tonnetz=True
            )
            return features
        except Exception as e:
            print(f"❌ Error extracting audio features: {e}")
            return None
    
    def predict_emotion_from_features(self, features: np.ndarray) -> Tuple[str, float]:
        """Predict emotion from extracted features."""
        if self.model_type == "ml_model" and self.model is not None:
            try:
                # Reshape features for prediction
                features_reshaped = features.reshape(1, -1)
                
                # Get prediction and confidence
                prediction = self.model.predict(features_reshaped)[0]
                
                # Try to get prediction probabilities for confidence
                if hasattr(self.model, 'predict_proba'):
                    probabilities = self.model.predict_proba(features_reshaped)[0]
                    confidence = float(np.max(probabilities))
                else:
                    confidence = 0.8  # Default confidence for models without probabilities
                
                return prediction, confidence
            except Exception as e:
                print(f"❌ Error in ML prediction: {e}")
                return self.rule_based_prediction(features)
        else:
            return self.rule_based_prediction(features)
    
    def rule_based_prediction(self, features: np.ndarray) -> Tuple[str, float]:
        """Simple rule-based emotion prediction based on audio features."""
        try:
            # Simple heuristics based on feature statistics
            mean_val = np.mean(features)
            std_val = np.std(features)
            energy = np.sum(features ** 2)
            
            # Normalize energy
            energy_norm = min(energy / 1000.0, 1.0)
            
            # Simple rules
            if energy_norm > 0.7 and std_val > 0.5:
                return "angry", 0.7
            elif energy_norm > 0.6 and mean_val > 0:
                return "happy", 0.6
            elif energy_norm < 0.3:
                return "sad", 0.6
            elif std_val < 0.2:
                return "calm", 0.5
            else:
                return "neutral", 0.5
                
        except Exception as e:
            print(f"❌ Error in rule-based prediction: {e}")
            return "neutral", 0.3
    
    def analyze_audio_file(self, audio_file_path: str) -> Dict[str, any]:
        """Analyze an audio file and return emotion prediction."""
        # Extract features
        features = self.extract_audio_features(audio_file_path)
        if features is None:
            return {"emotion": "neutral", "confidence": 0.0, "error": "Feature extraction failed"}
        
        # Predict emotion
        emotion, confidence = self.predict_emotion_from_features(features)
        
        # Ensure emotion is in available emotions
        if emotion not in AVAILABLE_EMOTIONS:
            emotion = "neutral"
            confidence = 0.3
        
        result = {
            "emotion": emotion,
            "confidence": confidence,
            "timestamp": time.time(),
            "model_type": self.model_type,
            "features_shape": features.shape
        }
        
        # Update emotion data
        self.update_emotion_data(result)
        
        return result
    
    def update_emotion_data(self, emotion_result: Dict[str, any]):
        """Update emotion data files with new result."""
        try:
            # Update main emotion data file
            emotion_data = {
                "current_emotion": emotion_result["emotion"],
                "confidence": emotion_result["confidence"],
                "last_updated": emotion_result["timestamp"],
                "model_type": emotion_result["model_type"]
            }
            
            with open(self.emotion_file, "w", encoding="utf-8") as f:
                json.dump(emotion_data, f, indent=2)
            
            # Update GUI emotion file
            with open(self.gui_emotion_file, "w", encoding="utf-8") as f:
                f.write(emotion_result["emotion"].capitalize())
            
            # Add to history
            self.add_emotion_record(
                emotion_result["emotion"],
                emotion_result["confidence"],
                f"Audio analysis ({emotion_result['model_type']})"
            )
            
        except Exception as e:
            print(f"❌ Error updating emotion data: {e}")
    
    def get_current_emotion(self) -> Dict[str, any]:
        """Get the current emotion state."""
        try:
            if os.path.exists(self.emotion_file):
                with open(self.emotion_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    return {
                        "emotion": data.get("current_emotion", "neutral"),
                        "confidence": data.get("confidence", 0.0),
                        "timestamp": data.get("last_updated", time.time()),
                        "model_type": data.get("model_type", "unknown")
                    }
        except Exception as e:
            print(f"Error reading emotion data: {e}")
        
        return {"emotion": "neutral", "confidence": 0.0, "timestamp": time.time(), "model_type": "default"}
    
    def get_emotion_context(self) -> Dict[str, any]:
        """Get the latest speech-emotion context."""
        try:
            if os.path.exists(self.context_file):
                with open(self.context_file, "r", encoding="utf-8") as f:
                    return json.load(f)
        except Exception as e:
            print(f"Error reading emotion context: {e}")
        
        return {"text": "", "emotion": "neutral", "confidence": 0.0, "timestamp": time.time()}
    
    def add_emotion_record(self, emotion: str, confidence: float, context: str = ""):
        """Add a new emotion record to history."""
        record = {
            "emotion": emotion,
            "confidence": confidence,
            "context": context,
            "timestamp": time.time()
        }
        
        self.emotion_history.append(record)
        self.save_emotion_history()
    
    def save_emotion_history(self):
        """Save emotion history to file."""
        try:
            data = {
                "history": list(self.emotion_history),
                "last_updated": time.time()
            }
            with open(self.history_file, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"Error saving emotion history: {e}")
    
    def load_emotion_history(self):
        """Load emotion history from file."""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    history = data.get("history", [])
                    self.emotion_history = deque(history[-30:], maxlen=50)
        except Exception as e:
            print(f"Error loading emotion history: {e}")

# Global emotion detector instance
emotion_detector = EmotionDetector()

# Convenience functions for backward compatibility
def get_current_emotion() -> Dict[str, any]:
    """Get current emotion state."""
    return emotion_detector.get_current_emotion()

def get_emotion_context() -> Dict[str, any]:
    """Get latest speech-emotion context."""
    return emotion_detector.get_emotion_context()

def analyze_audio_emotion(audio_file_path: str) -> Dict[str, any]:
    """Analyze emotion from audio file."""
    return emotion_detector.analyze_audio_file(audio_file_path)

def add_emotion_record(emotion: str, confidence: float, context: str = ""):
    """Add emotion record to history."""
    emotion_detector.add_emotion_record(emotion, confidence, context)
