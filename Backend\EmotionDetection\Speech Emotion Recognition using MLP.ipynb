{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "Speechemotion_mlp.ipynb", "provenance": [], "collapsed_sections": [], "toc_visible": true, "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/blhprasanna99/speech_emotion_detection/blob/master/Speechemotion_mlp.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "Pa5wKx0sWpkY", "colab_type": "text"}, "source": ["**SPEECH EMOTION DETECTION USING MLP**\n", "\n", "> Indented block\n", "\n"]}, {"cell_type": "code", "metadata": {"id": "bA0hIItWTBJA", "colab_type": "code", "outputId": "d56e7d20-0fd3-4daa-9660-0ba56ba662c5", "colab": {"base_uri": "https://localhost:8080/", "height": 122}}, "source": ["from google.colab import drive\n", "drive.mount('/content/drive')"], "execution_count": 0, "outputs": [{"output_type": "stream", "text": ["Go to this URL in a browser: https://accounts.google.com/o/oauth2/auth?client_id=************-6bn6qk8qdgf4n4g3pfee6491hc0brc4i.apps.googleusercontent.com&redirect_uri=urn%3aietf%3awg%3aoauth%3a2.0%3aoob&response_type=code&scope=email%20https%3a%2f%2fwww.googleapis.com%2fauth%2fdocs.test%20https%3a%2f%2fwww.googleapis.com%2fauth%2fdrive%20https%3a%2f%2fwww.googleapis.com%2fauth%2fdrive.photos.readonly%20https%3a%2f%2fwww.googleapis.com%2fauth%2fpeopleapi.readonly\n", "\n", "Enter your authorization code:\n", "··········\n", "Mounted at /content/drive\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "N0KnenRuT9M9", "colab_type": "code", "outputId": "5c5d46d1-6664-4a20-a4d8-28759c5ba027", "colab": {"base_uri": "https://localhost:8080/", "height": 89}}, "source": ["!pip install soundfile"], "execution_count": 0, "outputs": [{"output_type": "stream", "text": ["Requirement already satisfied: soundfile in /usr/local/lib/python3.6/dist-packages (0.10.3.post1)\n", "Requirement already satisfied: cffi>=1.0 in /usr/local/lib/python3.6/dist-packages (from soundfile) (1.14.0)\n", "Requirement already satisfied: pycparser in /usr/local/lib/python3.6/dist-packages (from cffi>=1.0->soundfile) (2.19)\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "fCsxrFLKT4xf", "colab_type": "code", "colab": {}}, "source": ["import soundfile\n", "import numpy as np\n", "import librosa\n", "import glob\n", "import os\n", "from sklearn.model_selection import train_test_split\n", "\n", "# all emotions on RAVDESS dataset\n", "int2emotion = {\n", "    \"01\": \"neutral\",\n", "    \"02\": \"calm\",\n", "    \"03\": \"happy\",\n", "    \"04\": \"sad\",\n", "    \"05\": \"angry\",\n", "    \"06\": \"fearful\",\n", "    \"07\": \"disgust\",\n", "    \"08\": \"surprised\"\n", "}\n", "\n", "# we allow only these emotions\n", "AVAILABLE_EMOTIONS = {\n", "    \"angry\",\n", "    \"sad\",\n", "    \"neutral\",\n", "    \"happy\"\n", "}\n"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "KWJ9phRhUDrM", "colab_type": "code", "colab": {}}, "source": ["def extract_feature(file_name, **kwargs):\n", "    \"\"\"\n", "    Extract feature from audio file `file_name`\n", "        Features supported:\n", "            - MFCC (mfcc)\n", "            - Chroma (chroma)\n", "            - MEL Spectrogram Frequency (mel)\n", "            - <PERSON><PERSON><PERSON> (contrast)\n", "            - Tonnetz (tonnetz)\n", "        e.g:\n", "        `features = extract_feature(path, mel=True, mfcc=True)`\n", "    \"\"\"\n", "    mfcc = kwargs.get(\"mfcc\")\n", "    chroma = kwargs.get(\"chroma\")\n", "    mel = kwargs.get(\"mel\")\n", "    contrast = kwargs.get(\"contrast\")\n", "    tonnetz = kwargs.get(\"tonnetz\")\n", "    with soundfile.SoundFile(file_name) as sound_file:\n", "        X = sound_file.read(dtype=\"float32\")\n", "        sample_rate = sound_file.samplerate\n", "        if chroma or contrast:\n", "            stft = np.abs(librosa.stft(X))\n", "        result = np.array([])\n", "        if mfcc:\n", "            mfccs = np.mean(librosa.feature.mfcc(y=X, sr=sample_rate, n_mfcc=40).T, axis=0)\n", "            result = np.hstack((result, mfccs))\n", "        if chroma:\n", "            chroma = np.mean(librosa.feature.chroma_stft(S=stft, sr=sample_rate).T,axis=0)\n", "            result = np.hstack((result, chroma))\n", "        if mel:\n", "            mel = np.mean(librosa.feature.melspectrogram(X, sr=sample_rate).T,axis=0)\n", "            result = np.hstack((result, mel))\n", "        if contrast:\n", "            contrast = np.mean(librosa.feature.spectral_contrast(S=stft, sr=sample_rate).T,axis=0)\n", "            result = np.hstack((result, contrast))\n", "        if tonnetz:\n", "            tonnetz = np.mean(librosa.feature.tonnetz(y=librosa.effects.harmonic(X), sr=sample_rate).T,axis=0)\n", "            result = np.hstack((result, tonnetz))\n", "    return result"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "oQIiWpQoUJli", "colab_type": "code", "colab": {}}, "source": ["def load_data(test_size=0.2):\n", "    X, y = [], []\n", "    try :\n", "      for file in glob.glob(\"/content/drive/My Drive/wav/Actor_*/*.wav\"):\n", "          # get the base name of the audio file\n", "          basename = os.path.basename(file)\n", "          print(basename)\n", "          # get the emotion label\n", "          emotion = int2emotion[basename.split(\"-\")[2]]\n", "          # we allow only AVAILABLE_EMOTIONS we set\n", "          if emotion not in AVAILABLE_EMOTIONS:\n", "              continue\n", "          # extract speech features\n", "          features = extract_feature(file, mfcc=True, chroma=True, mel=True)\n", "          # add to data\n", "          X.append(features)\n", "          y.append(emotion)\n", "    except :\n", "         pass\n", "    # split the data to training and testing and return it\n", "    return train_test_split(np.array(X), y, test_size=test_size, random_state=7)"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "fk7H7QV2UYSS", "colab_type": "code", "colab": {}}, "source": ["from sklearn.neural_network import MLPClassifier\n", "\n", "from sklearn.metrics import accuracy_score\n", "\n", "import os\n", "import pickle"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "5gkY1WH7UhIW", "colab_type": "code", "outputId": "fb783b4e-669b-4a15-8371-d9ddf1e5923f", "colab": {"base_uri": "https://localhost:8080/", "height": 1000}}, "source": ["X_train, X_test, y_train, y_test = load_data(test_size=0.25)\n", "# print some details\n", "# number of samples in training data\n", "print(\"[+] Number of training samples:\", X_train.shape[0])\n", "# number of samples in testing data\n", "print(\"[+] Number of testing samples:\", X_test.shape[0])\n", "# number of features used\n", "# this is a vector of features extracted \n", "# using utils.extract_features() method\n", "print(\"[+] Number of features:\", X_train.shape[1])\n", "# best model, determined by a grid search\n", "model_params = {\n", "    'alpha': 0.01,\n", "    'batch_size': 256,\n", "    'epsilon': 1e-08, \n", "    'hidden_layer_sizes': (300,), \n", "    'learning_rate': 'adaptive', \n", "    'max_iter': 500, \n", "}\n", "# initialize Multi Layer Perceptron classifier\n", "# with best parameters ( so far )\n", "model = MLPClassifier(**model_params)\n", "\n", "# train the model\n", "print(\"[*] Training the model...\")\n", "model.fit(X_train, y_train)\n", "\n", "# predict 25% of data to measure how good we are\n", "y_pred = model.predict(X_test)\n", "\n", "# calculate the accuracy\n", "accuracy = accuracy_score(y_true=y_test, y_pred=y_pred)\n", "\n", "print(\"Accuracy: {:.2f}%\".format(accuracy*100))\n", "\n", "# now we save the model\n", "# make result directory if doesn't exist yet\n", "if not os.path.isdir(\"result\"):\n", "    os.mkdir(\"result\")\n", "\n", "pickle.dump(model, open(\"result/mlp_classifier.model\", \"wb\"))"], "execution_count": 0, "outputs": [{"output_type": "stream", "text": ["03-01-01-01-01-01-21.wav\n", "03-01-01-01-02-02-21.wav\n", "03-01-01-01-01-02-21.wav\n", "03-01-02-01-01-01-21.wav\n", "03-01-02-01-01-02-21.wav\n", "03-01-02-01-02-01-21.wav\n", "03-01-01-01-02-01-21.wav\n", "03-01-02-01-02-02-21.wav\n", "03-01-02-02-01-01-21.wav\n", "03-01-02-02-02-01-21.wav\n", "03-01-02-02-02-02-21.wav\n", "03-01-02-02-01-02-21.wav\n", "03-01-03-01-02-02-21.wav\n", "03-01-03-01-01-01-21.wav\n", "03-01-03-01-02-01-21.wav\n", "03-01-03-01-01-02-21.wav\n", "03-01-03-02-02-02-21.wav\n", "03-01-03-02-01-01-21.wav\n", "03-01-03-02-02-01-21.wav\n", "03-01-03-02-01-02-21.wav\n", "03-01-04-01-01-01-21.wav\n", "03-01-04-01-01-02-21.wav\n", "03-01-04-01-02-01-21.wav\n", "03-01-04-01-02-02-21.wav\n", "03-01-05-01-01-02-21.wav\n", "03-01-04-02-01-01-21.wav\n", "03-01-04-02-01-02-21.wav\n", "03-01-05-01-02-01-21.wav\n", "03-01-04-02-02-02-21.wav\n", "03-01-05-01-01-01-21.wav\n", "03-01-05-02-01-01-21.wav\n", "03-01-05-01-02-02-21.wav\n", "03-01-04-02-02-01-21.wav\n", "03-01-06-01-02-02-21.wav\n", "03-01-05-02-02-01-21.wav\n", "03-01-06-02-01-01-21.wav\n", "03-01-05-02-01-02-21.wav\n", "03-01-06-02-01-02-21.wav\n", "03-01-06-02-02-01-21.wav\n", "03-01-06-01-01-01-21.wav\n", "03-01-06-01-01-02-21.wav\n", "03-01-05-02-02-02-21.wav\n", "03-01-06-01-02-01-21.wav\n", "03-01-06-02-02-02-21.wav\n", "03-01-07-01-01-02-21.wav\n", "03-01-07-01-01-01-21.wav\n", "03-01-07-01-02-02-21.wav\n", "03-01-07-01-02-01-21.wav\n", "03-01-07-02-02-02-21.wav\n", "03-01-07-02-01-02-21.wav\n", "03-01-07-02-01-01-21.wav\n", "03-01-08-01-01-02-21.wav\n", "03-01-07-02-02-01-21.wav\n", "03-01-08-01-01-01-21.wav\n", "03-01-08-01-02-02-21.wav\n", "03-01-08-01-02-01-21.wav\n", "03-01-08-02-01-01-21.wav\n", "03-01-08-02-01-02-21.wav\n", "03-01-08-02-02-02-21.wav\n", "03-01-08-02-02-01-21.wav\n", "03-02-01-01-02-02-21.wav\n", "03-02-01-01-02-01-21.wav\n", "03-02-01-01-01-01-21.wav\n", "03-02-01-01-01-02-21.wav\n", "03-02-02-01-01-02-21.wav\n", "03-02-02-01-01-01-21.wav\n", "03-02-02-01-02-01-21.wav\n", "03-02-02-01-02-02-21.wav\n", "03-02-02-02-01-01-21.wav\n", "03-02-02-02-01-02-21.wav\n", "03-02-03-01-01-02-21.wav\n", "03-02-02-02-02-01-21.wav\n", "03-02-03-01-02-01-21.wav\n", "03-02-03-01-01-01-21.wav\n", "03-02-03-01-02-02-21.wav\n", "03-02-02-02-02-02-21.wav\n", "03-02-03-02-01-01-21.wav\n", "03-02-03-02-01-02-21.wav\n", "03-02-04-01-02-02-21.wav\n", "03-02-03-02-02-01-21.wav\n", "03-02-04-02-01-01-21.wav\n", "03-02-03-02-02-02-21.wav\n", "03-02-04-02-01-02-21.wav\n", "03-02-04-01-01-01-21.wav\n", "03-02-04-02-02-01-21.wav\n", "03-02-04-01-02-01-21.wav\n", "03-02-04-01-01-02-21.wav\n", "03-02-04-02-02-02-21.wav\n", "03-02-05-01-01-01-21.wav\n", "03-02-05-01-02-02-21.wav\n", "03-02-05-02-01-01-21.wav\n", "03-02-05-01-01-02-21.wav\n", "03-02-05-01-02-01-21.wav\n", "03-02-05-02-01-02-21.wav\n", "03-02-05-02-02-01-21.wav\n", "03-02-05-02-02-02-21.wav\n", "03-02-06-01-02-01-21.wav\n", "03-02-06-01-01-01-21.wav\n", "03-02-06-01-01-02-21.wav\n", "03-02-06-02-01-01-21.wav\n", "03-02-06-01-02-02-21.wav\n", "03-02-06-02-02-01-21.wav\n", "03-02-06-02-01-02-21.wav\n", "03-02-06-02-02-02-21.wav\n", "03-01-01-01-01-01-16.wav\n", "03-01-01-01-01-02-16.wav\n", "03-01-01-01-02-02-16.wav\n", "03-01-01-01-02-01-16.wav\n", "03-01-02-01-01-01-16.wav\n", "03-01-02-01-01-02-16.wav\n", "03-01-02-01-02-01-16.wav\n", "03-01-02-01-02-02-16.wav\n", "03-01-02-02-02-01-16.wav\n", "03-01-02-02-01-02-16.wav\n", "03-01-02-02-02-02-16.wav\n", "03-01-02-02-01-01-16.wav\n", "03-01-03-01-01-02-16.wav\n", "03-01-03-01-01-01-16.wav\n", "03-01-03-01-02-01-16.wav\n", "03-01-03-01-02-02-16.wav\n", "03-01-03-02-01-01-16.wav\n", "03-01-04-01-01-01-16.wav\n", "03-01-03-02-02-01-16.wav\n", "03-01-04-01-01-02-16.wav\n", "03-01-03-02-02-02-16.wav\n", "03-01-03-02-01-02-16.wav\n", "03-01-04-01-02-01-16.wav\n", "03-01-04-02-01-01-16.wav\n", "03-01-04-01-02-02-16.wav\n", "03-01-04-02-01-02-16.wav\n", "03-01-04-02-02-01-16.wav\n", "03-01-04-02-02-02-16.wav\n", "03-01-05-01-02-01-16.wav\n", "03-01-05-01-01-01-16.wav\n", "03-01-05-01-02-02-16.wav\n", "03-01-05-01-01-02-16.wav\n", "03-01-05-02-01-01-16.wav\n", "03-01-05-02-01-02-16.wav\n", "03-01-05-02-02-01-16.wav\n", "03-01-05-02-02-02-16.wav\n", "03-01-06-01-01-01-16.wav\n", "03-01-06-02-01-01-16.wav\n", "03-01-06-01-02-01-16.wav\n", "03-01-06-01-01-02-16.wav\n", "03-01-06-02-01-02-16.wav\n", "03-01-06-01-02-02-16.wav\n", "03-01-06-02-02-01-16.wav\n", "03-01-06-02-02-02-16.wav\n", "03-01-07-01-01-01-16.wav\n", "03-01-07-01-01-02-16.wav\n", "03-01-08-01-01-02-16.wav\n", "03-01-07-01-02-02-16.wav\n", "03-01-07-01-02-01-16.wav\n", "03-01-07-02-02-01-16.wav\n", "03-01-07-02-01-02-16.wav\n", "03-01-07-02-01-01-16.wav\n", "03-01-08-01-01-01-16.wav\n", "03-01-07-02-02-02-16.wav\n", "03-01-08-01-02-01-16.wav\n", "03-02-01-01-01-01-16.wav\n", "03-01-08-01-02-02-16.wav\n", "03-02-01-01-01-02-16.wav\n", "03-01-08-02-01-01-16.wav\n", "03-01-08-02-01-02-16.wav\n", "03-01-08-02-02-01-16.wav\n", "03-01-08-02-02-02-16.wav\n", "03-02-01-01-02-01-16.wav\n", "03-02-01-01-02-02-16.wav\n", "03-02-02-01-01-01-16.wav\n", "03-02-02-02-02-01-16.wav\n", "03-02-02-01-01-02-16.wav\n", "03-02-02-02-02-02-16.wav\n", "03-02-02-01-02-01-16.wav\n", "03-02-02-01-02-02-16.wav\n", "03-02-03-01-01-02-16.wav\n", "03-02-03-01-01-01-16.wav\n", "03-02-02-02-01-02-16.wav\n", "03-02-02-02-01-01-16.wav\n", "03-02-03-01-02-01-16.wav\n", "03-02-03-02-02-01-16.wav\n", "03-02-03-01-02-02-16.wav\n", "03-02-03-02-02-02-16.wav\n", "03-02-03-02-01-01-16.wav\n", "03-02-04-01-01-01-16.wav\n", "03-02-03-02-01-02-16.wav\n", "03-02-04-01-01-02-16.wav\n", "03-02-04-01-02-01-16.wav\n", "03-02-04-01-02-02-16.wav\n", "03-02-04-02-01-01-16.wav\n", "03-02-04-02-02-01-16.wav\n", "03-02-04-02-01-02-16.wav\n", "03-02-05-01-01-01-16.wav\n", "03-02-04-02-02-02-16.wav\n", "03-02-05-01-01-02-16.wav\n", "03-02-05-01-02-01-16.wav\n", "03-02-05-01-02-02-16.wav\n", "03-02-05-02-01-02-16.wav\n", "03-02-05-02-02-01-16.wav\n", "03-02-05-02-01-01-16.wav\n", "03-02-05-02-02-02-16.wav\n", "03-02-06-01-01-01-16.wav\n", "03-02-06-01-01-02-16.wav\n", "03-02-06-01-02-01-16.wav\n", "03-02-06-02-01-01-16.wav\n", "03-02-06-01-02-02-16.wav\n", "03-02-06-02-01-02-16.wav\n", "03-02-06-02-02-01-16.wav\n", "03-02-06-02-02-02-16.wav\n", "03-01-01-01-01-01-23.wav\n", "03-01-01-01-01-02-23.wav\n", "03-01-01-01-02-01-23.wav\n", "03-01-01-01-02-02-23.wav\n", "03-01-02-01-01-01-23.wav\n", "03-01-02-01-01-02-23.wav\n", "03-01-02-01-02-02-23.wav\n", "03-01-02-01-02-01-23.wav\n", "03-01-02-02-02-01-23.wav\n", "03-01-02-02-01-01-23.wav\n", "03-01-02-02-01-02-23.wav\n", "03-01-03-01-01-01-23.wav\n", "03-01-02-02-02-02-23.wav\n", "03-01-03-01-01-02-23.wav\n", "03-01-03-01-02-02-23.wav\n", "03-01-03-01-02-01-23.wav\n", "03-01-03-02-01-01-23.wav\n", "03-01-03-02-01-02-23.wav\n", "03-01-03-02-02-01-23.wav\n", "03-01-04-01-01-01-23.wav\n", "03-01-03-02-02-02-23.wav\n", "03-01-04-01-02-02-23.wav\n", "03-01-04-01-02-01-23.wav\n", "03-01-04-01-01-02-23.wav\n", "03-01-04-02-02-01-23.wav\n", "03-01-04-02-01-02-23.wav\n", "03-01-04-02-01-01-23.wav\n", "03-01-05-01-01-01-23.wav\n", "03-01-05-02-01-02-23.wav\n", "03-01-04-02-02-02-23.wav\n", "03-01-05-01-01-02-23.wav\n", "03-01-05-02-02-02-23.wav\n", "03-01-05-02-02-01-23.wav\n", "03-01-05-01-02-01-23.wav\n", "03-01-05-02-01-01-23.wav\n", "03-01-05-01-02-02-23.wav\n", "03-01-06-01-01-01-23.wav\n", "03-01-06-01-01-02-23.wav\n", "03-01-06-02-02-02-23.wav\n", "03-01-06-01-02-01-23.wav\n", "03-01-06-01-02-02-23.wav\n", "03-01-06-02-01-01-23.wav\n", "03-01-07-01-01-01-23.wav\n", "03-01-07-01-01-02-23.wav\n", "03-01-06-02-02-01-23.wav\n", "03-01-06-02-01-02-23.wav\n", "03-01-07-01-02-01-23.wav\n", "03-01-07-01-02-02-23.wav\n", "03-01-07-02-01-01-23.wav\n", "03-01-07-02-01-02-23.wav\n", "03-01-07-02-02-01-23.wav\n", "03-01-07-02-02-02-23.wav\n", "03-01-08-01-01-02-23.wav\n", "03-01-08-01-01-01-23.wav\n", "03-01-08-01-02-01-23.wav\n", "03-01-08-01-02-02-23.wav\n", "03-02-01-01-01-02-23.wav\n", "03-01-08-02-01-01-23.wav\n", "03-01-08-02-02-02-23.wav\n", "03-02-01-01-02-01-23.wav\n", "03-01-08-02-01-02-23.wav\n", "03-02-01-01-02-02-23.wav\n", "03-01-08-02-02-01-23.wav\n", "03-02-01-01-01-01-23.wav\n", "03-02-02-01-01-01-23.wav\n", "03-02-02-01-01-02-23.wav\n", "03-02-02-02-02-02-23.wav\n", "03-02-02-01-02-01-23.wav\n", "03-02-03-01-01-01-23.wav\n", "03-02-02-01-02-02-23.wav\n", "03-02-03-01-01-02-23.wav\n", "03-02-02-02-01-01-23.wav\n", "03-02-03-01-02-01-23.wav\n", "03-02-02-02-01-02-23.wav\n", "03-02-02-02-02-01-23.wav\n", "03-02-03-01-02-02-23.wav\n", "03-02-03-02-01-01-23.wav\n", "03-02-03-02-02-02-23.wav\n", "03-02-04-01-01-01-23.wav\n", "03-02-04-01-01-02-23.wav\n", "03-02-04-01-02-01-23.wav\n", "03-02-04-01-02-02-23.wav\n", "03-02-04-02-01-01-23.wav\n", "03-02-03-02-01-02-23.wav\n", "03-02-03-02-02-01-23.wav\n", "03-02-04-02-01-02-23.wav\n", "03-02-04-02-02-01-23.wav\n", "03-02-05-01-02-02-23.wav\n", "03-02-04-02-02-02-23.wav\n", "03-02-05-02-01-01-23.wav\n", "03-02-05-01-01-01-23.wav\n", "03-02-05-02-01-02-23.wav\n", "03-02-05-01-02-01-23.wav\n", "03-02-05-02-02-01-23.wav\n", "03-02-05-01-01-02-23.wav\n", "03-02-05-02-02-02-23.wav\n", "03-02-06-01-01-01-23.wav\n", "03-02-06-01-02-02-23.wav\n", "03-02-06-01-01-02-23.wav\n", "03-02-06-02-01-01-23.wav\n", "03-02-06-01-02-01-23.wav\n", "03-02-06-02-01-02-23.wav\n", "03-02-06-02-02-01-23.wav\n", "03-02-06-02-02-02-23.wav\n", "03-01-01-01-01-01-19.wav\n", "03-01-01-01-01-02-19.wav\n", "03-01-01-01-02-01-19.wav\n", "03-01-02-02-01-02-19.wav\n", "03-01-01-01-02-02-19.wav\n", "03-01-02-02-01-01-19.wav\n", "03-01-02-01-01-01-19.wav\n", "03-01-02-02-02-01-19.wav\n", "03-01-02-01-02-01-19.wav\n", "03-01-02-01-01-02-19.wav\n", "03-01-02-02-02-02-19.wav\n", "03-01-02-01-02-02-19.wav\n", "03-01-03-01-01-01-19.wav\n", "03-01-03-02-01-01-19.wav\n", "03-01-03-01-01-02-19.wav\n", "03-01-03-01-02-01-19.wav\n", "03-01-03-02-01-02-19.wav\n", "03-01-03-01-02-02-19.wav\n", "03-01-03-02-02-01-19.wav\n", "03-01-03-02-02-02-19.wav\n", "03-01-04-01-01-01-19.wav\n", "03-01-04-01-02-01-19.wav\n", "03-01-04-01-01-02-19.wav\n", "03-01-04-01-02-02-19.wav\n", "03-01-04-02-01-01-19.wav\n", "03-01-04-02-01-02-19.wav\n", "03-01-04-02-02-01-19.wav\n", "03-01-04-02-02-02-19.wav\n", "03-01-05-01-01-01-19.wav\n", "03-01-05-02-01-01-19.wav\n", "03-01-05-01-02-01-19.wav\n", "03-01-05-02-01-02-19.wav\n", "03-01-05-01-01-02-19.wav\n", "03-01-05-02-02-01-19.wav\n", "03-01-05-01-02-02-19.wav\n", "03-01-05-02-02-02-19.wav\n", "03-01-06-01-01-01-19.wav\n", "03-01-06-02-02-01-19.wav\n", "03-01-06-01-01-02-19.wav\n", "03-01-06-02-02-02-19.wav\n", "03-01-06-01-02-02-19.wav\n", "03-01-07-01-01-01-19.wav\n", "03-01-06-02-01-01-19.wav\n", "03-01-07-01-01-02-19.wav\n", "03-01-06-02-01-02-19.wav\n", "03-01-06-01-02-01-19.wav\n", "03-01-07-01-02-01-19.wav\n", "03-01-07-01-02-02-19.wav\n", "03-01-07-02-01-01-19.wav\n", "03-01-07-02-02-01-19.wav\n", "03-01-07-02-01-02-19.wav\n", "03-01-08-01-01-01-19.wav\n", "03-01-07-02-02-02-19.wav\n", "03-01-08-01-02-02-19.wav\n", "03-01-08-01-01-02-19.wav\n", "03-01-08-02-01-01-19.wav\n", "03-01-08-01-02-01-19.wav\n", "03-01-08-02-02-01-19.wav\n", "03-01-08-02-01-02-19.wav\n", "03-01-08-02-02-02-19.wav\n", "03-02-01-01-01-02-19.wav\n", "03-02-01-01-02-01-19.wav\n", "03-02-01-01-01-01-19.wav\n", "03-02-02-01-02-02-19.wav\n", "03-02-02-01-01-01-19.wav\n", "03-02-01-01-02-02-19.wav\n", "03-02-02-01-02-01-19.wav\n", "03-02-02-01-01-02-19.wav\n", "03-02-02-02-01-01-19.wav\n", "03-02-02-02-01-02-19.wav\n", "03-02-02-02-02-01-19.wav\n", "03-02-02-02-02-02-19.wav\n", "03-02-03-02-01-01-19.wav\n", "03-02-03-01-01-01-19.wav\n", "03-02-03-01-01-02-19.wav\n", "03-02-03-01-02-02-19.wav\n", "03-02-03-02-01-02-19.wav\n", "03-02-03-02-02-02-19.wav\n", "03-02-03-02-02-01-19.wav\n", "03-02-03-01-02-01-19.wav\n", "03-02-04-01-01-01-19.wav\n", "03-02-04-01-01-02-19.wav\n", "03-02-04-02-01-01-19.wav\n", "03-02-04-01-02-01-19.wav\n", "03-02-04-02-01-02-19.wav\n", "03-02-04-01-02-02-19.wav\n", "03-02-04-02-02-01-19.wav\n", "03-02-05-01-02-01-19.wav\n", "03-02-04-02-02-02-19.wav\n", "03-02-05-01-02-02-19.wav\n", "03-02-05-01-01-01-19.wav\n", "03-02-05-02-01-01-19.wav\n", "03-02-05-01-01-02-19.wav\n", "03-02-05-02-01-02-19.wav\n", "03-02-05-02-02-01-19.wav\n", "03-02-06-02-01-02-19.wav\n", "03-02-06-01-01-01-19.wav\n", "03-02-06-02-01-01-19.wav\n", "03-02-06-02-02-01-19.wav\n", "03-02-05-02-02-02-19.wav\n", "03-02-06-02-02-02-19.wav\n", "03-02-06-01-01-02-19.wav\n", "03-02-06-01-02-01-19.wav\n", "03-02-06-01-02-02-19.wav\n", "03-01-01-01-02-02-20.wav\n", "03-01-02-01-01-01-20.wav\n", "03-01-01-01-01-01-20.wav\n", "03-01-02-01-01-02-20.wav\n", "03-01-01-01-02-01-20.wav\n", "03-01-02-01-02-01-20.wav\n", "03-01-01-01-01-02-20.wav\n", "03-01-02-01-02-02-20.wav\n", "03-01-02-02-02-02-20.wav\n", "03-01-02-02-01-01-20.wav\n", "03-01-03-01-01-01-20.wav\n", "03-01-02-02-01-02-20.wav\n", "03-01-03-01-01-02-20.wav\n", "03-01-02-02-02-01-20.wav\n", "03-01-03-01-02-02-20.wav\n", "03-01-03-02-01-02-20.wav\n", "03-01-03-02-01-01-20.wav\n", "03-01-03-02-02-01-20.wav\n", "03-01-03-02-02-02-20.wav\n", "03-01-04-01-01-01-20.wav\n", "03-01-04-01-02-01-20.wav\n", "03-01-04-01-01-02-20.wav\n", "03-01-04-01-02-02-20.wav\n", "03-01-04-02-01-01-20.wav\n", "03-01-04-02-02-02-20.wav\n", "03-01-04-02-02-01-20.wav\n", "03-01-05-01-01-01-20.wav\n", "03-01-04-02-01-02-20.wav\n", "03-01-05-01-01-02-20.wav\n", "03-01-05-01-02-01-20.wav\n", "03-01-05-01-02-02-20.wav\n", "03-01-05-02-01-01-20.wav\n", "03-01-05-02-01-02-20.wav\n", "03-01-05-02-02-01-20.wav\n", "03-01-06-01-02-02-20.wav\n", "03-01-06-01-01-01-20.wav\n", "03-01-05-02-02-02-20.wav\n", "03-01-06-01-02-01-20.wav\n", "03-01-06-02-01-01-20.wav\n", "03-01-06-01-01-02-20.wav\n", "03-01-06-02-01-02-20.wav\n", "03-01-06-02-02-01-20.wav\n", "03-01-06-02-02-02-20.wav\n", "03-01-07-01-01-01-20.wav\n", "03-01-07-02-01-01-20.wav\n", "03-01-07-01-01-02-20.wav\n", "03-01-07-01-02-01-20.wav\n", "03-01-07-02-01-02-20.wav\n", "03-01-07-01-02-02-20.wav\n", "03-01-08-01-01-01-20.wav\n", "03-01-07-02-02-02-20.wav\n", "03-01-07-02-02-01-20.wav\n", "03-01-08-01-01-02-20.wav\n", "03-01-08-01-02-01-20.wav\n", "03-01-08-02-01-02-20.wav\n", "03-01-08-01-02-02-20.wav\n", "03-01-08-02-01-01-20.wav\n", "03-01-08-02-02-01-20.wav\n", "03-02-01-01-01-01-20.wav\n", "03-01-08-02-02-02-20.wav\n", "03-02-01-01-01-02-20.wav\n", "03-02-01-01-02-01-20.wav\n", "03-02-01-01-02-02-20.wav\n", "03-02-02-02-01-01-20.wav\n", "03-02-02-01-01-01-20.wav\n", "03-02-02-01-01-02-20.wav\n", "03-02-02-02-01-02-20.wav\n", "03-02-02-01-02-02-20.wav\n", "03-02-02-02-02-01-20.wav\n", "03-02-02-01-02-01-20.wav\n", "03-02-02-02-02-02-20.wav\n", "03-02-03-01-01-01-20.wav\n", "03-02-03-01-01-02-20.wav\n", "03-02-03-02-02-01-20.wav\n", "03-02-03-01-02-01-20.wav\n", "03-02-04-01-01-01-20.wav\n", "03-02-03-01-02-02-20.wav\n", "03-02-03-02-02-02-20.wav\n", "03-02-03-02-01-02-20.wav\n", "03-02-04-01-01-02-20.wav\n", "03-02-03-02-01-01-20.wav\n", "03-02-04-01-02-01-20.wav\n", "03-02-04-01-02-02-20.wav\n", "03-02-04-02-02-01-20.wav\n", "03-02-04-02-01-01-20.wav\n", "03-02-04-02-02-02-20.wav\n", "03-02-04-02-01-02-20.wav\n", "03-02-05-01-01-01-20.wav\n", "03-02-05-01-02-01-20.wav\n", "03-02-05-01-01-02-20.wav\n", "03-02-05-02-01-01-20.wav\n", "03-02-05-01-02-02-20.wav\n", "03-02-05-02-02-01-20.wav\n", "03-02-05-02-01-02-20.wav\n", "03-02-05-02-02-02-20.wav\n", "03-02-06-01-01-01-20.wav\n", "03-02-06-01-01-02-20.wav\n", "03-02-06-01-02-01-20.wav\n", "03-02-06-02-01-02-20.wav\n", "03-02-06-02-02-01-20.wav\n", "03-02-06-01-02-02-20.wav\n", "03-02-06-02-02-02-20.wav\n", "03-02-06-02-01-01-20.wav\n", "03-01-01-01-01-01-15.wav\n", "03-01-01-01-01-02-15.wav\n", "03-01-01-01-02-01-15.wav\n", "03-01-01-01-02-02-15.wav\n", "03-01-02-01-01-02-15.wav\n", "03-01-02-02-02-01-15.wav\n", "03-01-02-01-02-01-15.wav\n", "03-01-02-01-01-01-15.wav\n", "03-01-03-01-01-01-15.wav\n", "03-01-02-02-01-02-15.wav\n", "03-01-02-02-01-01-15.wav\n", "03-01-02-01-02-02-15.wav\n", "03-01-02-02-02-02-15.wav\n", "03-01-03-01-01-02-15.wav\n", "03-01-03-01-02-01-15.wav\n", "03-01-03-01-02-02-15.wav\n", "03-01-03-02-01-01-15.wav\n", "03-01-04-01-01-01-15.wav\n", "03-01-03-02-01-02-15.wav\n", "03-01-03-02-02-01-15.wav\n", "03-01-03-02-02-02-15.wav\n", "03-01-04-01-02-01-15.wav\n", "03-01-04-01-02-02-15.wav\n", "03-01-04-01-01-02-15.wav\n", "03-01-04-02-01-01-15.wav\n", "03-01-05-01-02-01-15.wav\n", "03-01-04-02-01-02-15.wav\n", "03-01-05-01-02-02-15.wav\n", "03-01-04-02-02-02-15.wav\n", "03-01-04-02-02-01-15.wav\n", "03-01-05-02-01-01-15.wav\n", "03-01-05-02-01-02-15.wav\n", "03-01-05-01-01-01-15.wav\n", "03-01-05-01-01-02-15.wav\n", "03-01-05-02-02-01-15.wav\n", "03-01-05-02-02-02-15.wav\n", "03-01-06-01-01-01-15.wav\n", "03-01-06-02-01-01-15.wav\n", "03-01-06-01-02-01-15.wav\n", "03-01-06-02-01-02-15.wav\n", "03-01-06-01-01-02-15.wav\n", "03-01-06-01-02-02-15.wav\n", "03-01-06-02-02-02-15.wav\n", "03-01-06-02-02-01-15.wav\n", "03-01-07-01-01-01-15.wav\n", "03-01-07-01-01-02-15.wav\n", "03-01-07-01-02-01-15.wav\n", "03-01-07-02-01-01-15.wav\n", "03-01-07-01-02-02-15.wav\n", "03-01-07-02-01-02-15.wav\n", "03-01-07-02-02-01-15.wav\n", "03-01-08-01-01-01-15.wav\n", "03-01-08-01-01-02-15.wav\n", "03-01-07-02-02-02-15.wav\n", "03-01-08-01-02-01-15.wav\n", "03-01-08-02-01-02-15.wav\n", "03-01-08-01-02-02-15.wav\n", "03-01-08-02-01-01-15.wav\n", "03-01-08-02-02-01-15.wav\n", "03-02-01-01-01-02-15.wav\n", "03-02-01-01-01-01-15.wav\n", "03-01-08-02-02-02-15.wav\n", "03-02-02-01-01-01-15.wav\n", "03-02-01-01-02-02-15.wav\n", "03-02-01-01-02-01-15.wav\n", "03-02-02-01-02-01-15.wav\n", "03-02-02-01-01-02-15.wav\n", "03-02-02-01-02-02-15.wav\n", "03-02-02-02-01-01-15.wav\n", "03-02-03-01-02-01-15.wav\n", "03-02-02-02-02-01-15.wav\n", "03-02-02-02-01-02-15.wav\n", "03-02-03-01-02-02-15.wav\n", "03-02-02-02-02-02-15.wav\n", "03-02-03-01-01-01-15.wav\n", "03-02-03-02-01-02-15.wav\n", "03-02-03-02-01-01-15.wav\n", "03-02-03-01-01-02-15.wav\n", "03-02-03-02-02-01-15.wav\n", "03-02-04-01-02-01-15.wav\n", "03-02-03-02-02-02-15.wav\n", "03-02-04-01-02-02-15.wav\n", "03-02-04-01-01-01-15.wav\n", "03-02-04-01-01-02-15.wav\n", "03-02-04-02-02-02-15.wav\n", "03-02-04-02-01-02-15.wav\n", "03-02-04-02-01-01-15.wav\n", "03-02-04-02-02-01-15.wav\n", "03-02-05-01-01-01-15.wav\n", "03-02-05-01-01-02-15.wav\n", "03-02-05-01-02-01-15.wav\n", "03-02-05-02-01-01-15.wav\n", "03-02-05-01-02-02-15.wav\n", "03-02-05-02-01-02-15.wav\n", "03-02-05-02-02-01-15.wav\n", "03-02-05-02-02-02-15.wav\n", "03-02-06-01-01-01-15.wav\n", "03-02-06-01-01-02-15.wav\n", "03-02-06-01-02-01-15.wav\n", "03-02-06-02-01-01-15.wav\n", "03-02-06-01-02-02-15.wav\n", "03-02-06-02-02-01-15.wav\n", "03-02-06-02-01-02-15.wav\n", "03-02-06-02-02-02-15.wav\n", "03-01-01-01-02-01-17.wav\n", "03-01-01-01-01-01-17.wav\n", "03-01-01-01-02-02-17.wav\n", "03-01-01-01-01-02-17.wav\n", "03-01-02-01-01-01-17.wav\n", "03-01-02-01-02-01-17.wav\n", "03-01-02-02-01-01-17.wav\n", "03-01-02-01-01-02-17.wav\n", "03-01-02-02-01-02-17.wav\n", "03-01-02-01-02-02-17.wav\n", "03-01-02-02-02-01-17.wav\n", "03-01-02-02-02-02-17.wav\n", "03-01-03-01-01-01-17.wav\n", "03-01-03-01-01-02-17.wav\n", "03-01-03-01-02-01-17.wav\n", "03-01-03-02-02-01-17.wav\n", "03-01-03-01-02-02-17.wav\n", "03-01-03-02-02-02-17.wav\n", "03-01-03-02-01-01-17.wav\n", "03-01-04-01-01-01-17.wav\n", "03-01-03-02-01-02-17.wav\n", "03-01-04-01-01-02-17.wav\n", "03-01-04-01-02-01-17.wav\n", "03-01-05-01-01-01-17.wav\n", "03-01-04-01-02-02-17.wav\n", "03-01-05-01-01-02-17.wav\n", "03-01-04-02-01-02-17.wav\n", "03-01-04-02-01-01-17.wav\n", "03-01-05-01-02-01-17.wav\n", "03-01-05-01-02-02-17.wav\n", "03-01-04-02-02-02-17.wav\n", "03-01-04-02-02-01-17.wav\n", "03-01-05-02-01-01-17.wav\n", "03-01-05-02-02-01-17.wav\n", "03-01-05-02-01-02-17.wav\n", "03-01-05-02-02-02-17.wav\n", "03-01-06-01-01-01-17.wav\n", "03-01-06-01-02-01-17.wav\n", "03-01-06-01-01-02-17.wav\n", "03-01-06-01-02-02-17.wav\n", "03-01-06-02-02-01-17.wav\n", "03-01-06-02-01-02-17.wav\n", "03-01-06-02-01-01-17.wav\n", "03-01-06-02-02-02-17.wav\n", "03-01-07-01-01-01-17.wav\n", "03-01-07-01-01-02-17.wav\n", "03-01-07-01-02-01-17.wav\n", "03-01-08-01-01-01-17.wav\n", "03-01-07-02-01-02-17.wav\n", "03-01-07-02-02-01-17.wav\n", "03-01-07-01-02-02-17.wav\n", "03-01-07-02-01-01-17.wav\n", "03-01-08-01-01-02-17.wav\n", "03-01-07-02-02-02-17.wav\n", "03-01-08-01-02-02-17.wav\n", "03-01-08-01-02-01-17.wav\n", "03-01-08-02-01-01-17.wav\n", "03-02-01-01-02-01-17.wav\n", "03-01-08-02-01-02-17.wav\n", "03-01-08-02-02-01-17.wav\n", "03-02-01-01-02-02-17.wav\n", "03-02-02-01-01-01-17.wav\n", "03-02-02-01-01-02-17.wav\n", "03-02-02-01-02-01-17.wav\n", "03-02-02-02-01-01-17.wav\n", "03-02-02-01-02-02-17.wav\n", "03-02-02-02-01-02-17.wav\n", "03-01-08-02-02-02-17.wav\n", "03-02-01-01-01-01-17.wav\n", "03-02-01-01-01-02-17.wav\n", "03-02-02-02-02-01-17.wav\n", "03-02-03-01-01-01-17.wav\n", "03-02-02-02-02-02-17.wav\n", "03-02-03-01-01-02-17.wav\n", "03-02-03-01-02-02-17.wav\n", "03-02-03-01-02-01-17.wav\n", "03-02-03-02-01-01-17.wav\n", "03-02-04-01-01-01-17.wav\n", "03-02-03-02-02-01-17.wav\n", "03-02-04-01-01-02-17.wav\n", "03-02-03-02-01-02-17.wav\n", "03-02-03-02-02-02-17.wav\n", "03-02-04-01-02-02-17.wav\n", "03-02-04-01-02-01-17.wav\n", "03-02-04-02-01-01-17.wav\n", "03-02-04-02-01-02-17.wav\n", "03-02-05-01-02-01-17.wav\n", "03-02-04-02-02-01-17.wav\n", "03-02-05-01-02-02-17.wav\n", "03-02-04-02-02-02-17.wav\n", "03-02-05-02-01-02-17.wav\n", "03-02-05-02-01-01-17.wav\n", "03-02-05-01-01-01-17.wav\n", "03-02-05-01-01-02-17.wav\n", "03-02-05-02-02-01-17.wav\n", "03-02-06-02-01-01-17.wav\n", "03-02-05-02-02-02-17.wav\n", "03-02-06-02-01-02-17.wav\n", "03-02-06-01-01-01-17.wav\n", "03-02-06-01-02-01-17.wav\n", "03-02-06-01-01-02-17.wav\n", "03-02-06-02-02-01-17.wav\n", "03-02-06-01-02-02-17.wav\n", "03-02-06-02-02-02-17.wav\n", "03-01-01-01-01-02-22.wav\n", "03-01-01-01-01-01-22.wav\n", "03-01-01-01-02-01-22.wav\n", "03-01-01-01-02-02-22.wav\n", "03-01-02-01-01-01-22.wav\n", "03-01-02-01-01-02-22.wav\n", "03-01-02-01-02-02-22.wav\n", "03-01-02-01-02-01-22.wav\n", "03-01-02-02-01-01-22.wav\n", "03-01-02-02-01-02-22.wav\n", "03-01-02-02-02-02-22.wav\n", "03-01-03-01-01-01-22.wav\n", "03-01-02-02-02-01-22.wav\n", "03-01-03-01-02-01-22.wav\n", "03-01-03-01-01-02-22.wav\n", "03-01-03-01-02-02-22.wav\n", "03-01-04-01-01-02-22.wav\n", "03-01-03-02-01-01-22.wav\n", "03-01-04-01-02-01-22.wav\n", "03-01-03-02-01-02-22.wav\n", "03-01-04-01-02-02-22.wav\n", "03-01-04-01-01-01-22.wav\n", "03-01-03-02-02-01-22.wav\n", "03-01-04-02-01-01-22.wav\n", "03-01-03-02-02-02-22.wav\n", "03-01-04-02-01-02-22.wav\n", "03-01-05-01-01-02-22.wav\n", "03-01-04-02-02-01-22.wav\n", "03-01-05-01-02-01-22.wav\n", "03-01-04-02-02-02-22.wav\n", "03-01-05-01-02-02-22.wav\n", "03-01-05-01-01-01-22.wav\n", "03-01-05-02-01-01-22.wav\n", "03-01-05-02-01-02-22.wav\n", "03-01-05-02-02-01-22.wav\n", "03-01-06-01-01-01-22.wav\n", "03-01-06-01-01-02-22.wav\n", "03-01-05-02-02-02-22.wav\n", "03-01-06-01-02-02-22.wav\n", "03-01-06-01-02-01-22.wav\n", "03-01-06-02-01-01-22.wav\n", "03-01-06-02-01-02-22.wav\n", "03-01-06-02-02-02-22.wav\n", "03-01-06-02-02-01-22.wav\n", "03-01-07-01-01-02-22.wav\n", "03-01-07-01-01-01-22.wav\n", "03-01-07-01-02-02-22.wav\n", "03-01-07-01-02-01-22.wav\n", "03-01-07-02-01-01-22.wav\n", "03-01-07-02-02-02-22.wav\n", "03-01-07-02-01-02-22.wav\n", "03-01-07-02-02-01-22.wav\n", "03-01-08-01-02-02-22.wav\n", "03-01-08-01-01-01-22.wav\n", "03-01-08-02-01-01-22.wav\n", "03-01-08-01-01-02-22.wav\n", "03-01-08-02-01-02-22.wav\n", "03-01-08-01-02-01-22.wav\n", "03-01-08-02-02-01-22.wav\n", "03-01-08-02-02-02-22.wav\n", "03-02-02-01-01-02-22.wav\n", "03-02-01-01-01-01-22.wav\n", "03-02-02-01-02-01-22.wav\n", "03-02-01-01-01-02-22.wav\n", "03-02-02-01-02-02-22.wav\n", "03-02-01-01-02-02-22.wav\n", "03-02-02-02-01-01-22.wav\n", "03-02-01-01-02-01-22.wav\n", "03-02-02-01-01-01-22.wav\n", "03-02-02-02-01-02-22.wav\n", "03-02-02-02-02-02-22.wav\n", "03-02-03-01-01-02-22.wav\n", "03-02-02-02-02-01-22.wav\n", "03-02-03-01-02-01-22.wav\n", "03-02-03-01-01-01-22.wav\n", "03-02-03-01-02-02-22.wav\n", "03-02-03-02-01-01-22.wav\n", "03-02-03-02-02-01-22.wav\n", "03-02-03-02-01-02-22.wav\n", "03-02-03-02-02-02-22.wav\n", "03-02-04-01-02-01-22.wav\n", "03-02-04-01-01-02-22.wav\n", "03-02-04-01-01-01-22.wav\n", "03-02-04-01-02-02-22.wav\n", "03-02-04-02-01-02-22.wav\n", "03-02-04-02-01-01-22.wav\n", "03-02-05-01-01-02-22.wav\n", "03-02-04-02-02-01-22.wav\n", "03-02-05-01-02-01-22.wav\n", "03-02-04-02-02-02-22.wav\n", "03-02-05-02-01-01-22.wav\n", "03-02-05-01-02-02-22.wav\n", "03-02-05-01-01-01-22.wav\n", "03-02-05-02-01-02-22.wav\n", "03-02-06-01-01-02-22.wav\n", "03-02-05-02-02-01-22.wav\n", "03-02-06-01-02-02-22.wav\n", "03-02-06-01-02-01-22.wav\n", "03-02-05-02-02-02-22.wav\n", "03-02-06-01-01-01-22.wav\n", "03-02-06-02-02-01-22.wav\n", "03-02-06-02-01-02-22.wav\n", "03-02-06-02-01-01-22.wav\n", "03-02-06-02-02-02-22.wav\n", "03-01-01-01-01-01-24.wav\n", "03-01-02-01-02-01-24.wav\n", "03-01-01-01-01-02-24.wav\n", "03-01-02-01-02-02-24.wav\n", "03-01-01-01-02-01-24.wav\n", "03-01-02-02-01-01-24.wav\n", "03-01-01-01-02-02-24.wav\n", "03-01-02-02-01-02-24.wav\n", "03-01-02-01-01-01-24.wav\n", "03-01-02-01-01-02-24.wav\n", "03-01-02-02-02-01-24.wav\n", "03-01-02-02-02-02-24.wav\n", "03-01-03-01-01-01-24.wav\n", "03-01-03-01-02-01-24.wav\n", "03-01-03-01-01-02-24.wav\n", "03-01-03-01-02-02-24.wav\n", "03-01-03-02-01-01-24.wav\n", "03-01-03-02-01-02-24.wav\n", "03-01-03-02-02-02-24.wav\n", "03-01-03-02-02-01-24.wav\n", "03-01-04-01-01-01-24.wav\n", "03-01-04-01-02-01-24.wav\n", "03-01-04-01-01-02-24.wav\n", "03-01-04-02-01-01-24.wav\n", "03-01-04-01-02-02-24.wav\n", "03-01-04-02-02-01-24.wav\n", "03-01-04-02-01-02-24.wav\n", "03-01-04-02-02-02-24.wav\n", "03-01-05-01-02-01-24.wav\n", "03-01-05-01-01-01-24.wav\n", "03-01-05-01-01-02-24.wav\n", "03-01-05-01-02-02-24.wav\n", "03-01-05-02-01-01-24.wav\n", "03-01-05-02-01-02-24.wav\n", "03-01-05-02-02-01-24.wav\n", "03-01-05-02-02-02-24.wav\n", "03-01-06-02-01-01-24.wav\n", "03-01-06-01-01-01-24.wav\n", "03-01-06-01-01-02-24.wav\n", "03-01-06-02-01-02-24.wav\n", "03-01-06-01-02-02-24.wav\n", "03-01-06-01-02-01-24.wav\n", "03-01-06-02-02-02-24.wav\n", "03-01-06-02-02-01-24.wav\n", "03-01-07-01-01-01-24.wav\n", "03-01-07-02-02-01-24.wav\n", "03-01-07-01-01-02-24.wav\n", "03-01-07-01-02-02-24.wav\n", "03-01-07-01-02-01-24.wav\n", "03-01-07-02-02-02-24.wav\n", "03-01-08-01-01-01-24.wav\n", "03-01-07-02-01-01-24.wav\n", "03-01-07-02-01-02-24.wav\n", "03-01-08-01-01-02-24.wav\n", "03-01-08-01-02-01-24.wav\n", "03-01-08-01-02-02-24.wav\n", "03-01-08-02-02-01-24.wav\n", "03-01-08-02-01-01-24.wav\n", "03-01-08-02-02-02-24.wav\n", "03-02-01-01-02-01-24.wav\n", "03-02-01-01-01-02-24.wav\n", "03-01-08-02-01-02-24.wav\n", "03-02-02-01-01-01-24.wav\n", "03-02-01-01-02-02-24.wav\n", "03-02-02-01-02-01-24.wav\n", "03-02-02-01-02-02-24.wav\n", "03-02-02-02-01-01-24.wav\n", "03-02-02-01-01-02-24.wav\n", "03-02-02-02-02-01-24.wav\n", "03-02-02-02-01-02-24.wav\n", "03-02-03-01-02-01-24.wav\n", "03-02-03-01-01-01-24.wav\n", "03-02-02-02-02-02-24.wav\n", "03-02-03-02-01-01-24.wav\n", "03-02-03-01-01-02-24.wav\n", "03-02-03-01-02-02-24.wav\n", "03-02-03-02-01-02-24.wav\n", "03-02-03-02-02-01-24.wav\n", "03-02-04-01-02-02-24.wav\n", "03-02-03-02-02-02-24.wav\n", "03-02-04-02-01-01-24.wav\n", "03-02-04-01-02-01-24.wav\n", "03-02-04-01-01-01-24.wav\n", "03-02-04-02-01-02-24.wav\n", "03-02-04-01-01-02-24.wav\n", "03-02-04-02-02-01-24.wav\n", "03-02-04-02-02-02-24.wav\n", "03-02-05-02-01-02-24.wav\n", "03-02-05-01-01-01-24.wav\n", "03-02-05-02-02-01-24.wav\n", "03-02-05-01-01-02-24.wav\n", "03-02-05-02-02-02-24.wav\n", "03-02-05-01-02-01-24.wav\n", "03-02-05-01-02-02-24.wav\n", "03-02-06-01-01-01-24.wav\n", "03-02-05-02-01-01-24.wav\n", "03-02-06-01-02-01-24.wav\n", "03-02-06-01-01-02-24.wav\n", "03-02-06-02-01-02-24.wav\n", "03-02-06-01-02-02-24.wav\n", "03-02-06-02-02-01-24.wav\n", "03-02-06-02-01-01-24.wav\n", "03-02-06-02-02-02-24.wav\n", "03-01-01-01-01-01-18.wav\n", "03-01-01-01-01-02-18.wav\n", "03-01-01-01-02-01-18.wav\n", "03-01-02-01-01-01-18.wav\n", "03-01-01-01-02-02-18.wav\n", "03-01-02-01-01-02-18.wav\n", "03-01-02-01-02-01-18.wav\n", "03-01-02-01-02-02-18.wav\n", "03-01-02-02-02-01-18.wav\n", "03-01-02-02-01-01-18.wav\n", "03-01-02-02-02-02-18.wav\n", "03-01-02-02-01-02-18.wav\n", "03-01-03-01-01-02-18.wav\n", "03-01-03-01-01-01-18.wav\n", "03-01-03-01-02-01-18.wav\n", "03-01-03-01-02-02-18.wav\n", "03-01-04-01-01-01-18.wav\n", "03-01-03-02-01-01-18.wav\n", "03-01-04-01-01-02-18.wav\n", "03-01-03-02-01-02-18.wav\n", "03-01-04-01-02-01-18.wav\n", "03-01-03-02-02-01-18.wav\n", "03-01-04-01-02-02-18.wav\n", "03-01-03-02-02-02-18.wav\n", "03-01-04-02-01-01-18.wav\n", "03-01-05-01-01-01-18.wav\n", "03-01-04-02-01-02-18.wav\n", "03-01-05-01-02-01-18.wav\n", "03-01-04-02-02-01-18.wav\n", "03-01-05-01-01-02-18.wav\n", "03-01-04-02-02-02-18.wav\n", "03-01-05-01-02-02-18.wav\n", "03-01-05-02-01-01-18.wav\n", "03-01-05-02-02-01-18.wav\n", "03-01-05-02-01-02-18.wav\n", "03-01-06-01-01-01-18.wav\n", "03-01-06-01-01-02-18.wav\n", "03-01-05-02-02-02-18.wav\n", "03-01-06-01-02-01-18.wav\n", "03-01-06-01-02-02-18.wav\n", "03-01-07-01-01-02-18.wav\n", "03-01-06-02-02-01-18.wav\n", "03-01-06-02-01-01-18.wav\n", "03-01-06-02-01-02-18.wav\n", "03-01-07-01-02-01-18.wav\n", "03-01-07-01-01-01-18.wav\n", "03-01-06-02-02-02-18.wav\n", "03-01-07-01-02-02-18.wav\n", "03-01-07-02-01-01-18.wav\n", "03-01-08-02-01-02-18.wav\n", "03-01-07-02-01-02-18.wav\n", "03-01-08-01-02-01-18.wav\n", "03-01-07-02-02-01-18.wav\n", "03-01-08-01-02-02-18.wav\n", "03-01-07-02-02-02-18.wav\n", "03-01-08-02-01-01-18.wav\n", "03-01-08-01-01-01-18.wav\n", "03-01-08-01-01-02-18.wav\n", "03-01-08-02-02-01-18.wav\n", "03-01-08-02-02-02-18.wav\n", "03-01-01-01-02-01-10.wav\n", "03-01-01-01-01-01-10.wav\n", "03-01-01-01-01-02-10.wav\n", "03-01-01-01-02-02-10.wav\n", "03-01-02-01-01-01-10.wav\n", "03-01-02-01-01-02-10.wav\n", "03-01-02-01-02-01-10.wav\n", "03-01-02-01-02-02-10.wav\n", "03-01-03-01-01-01-10.wav\n", "03-01-02-02-01-01-10.wav\n", "03-01-03-01-01-02-10.wav\n", "03-01-02-02-01-02-10.wav\n", "03-01-03-01-02-01-10.wav\n", "03-01-02-02-02-01-10.wav\n", "03-01-02-02-02-02-10.wav\n", "03-01-03-01-02-02-10.wav\n", "03-01-03-02-01-01-10.wav\n", "03-01-04-01-02-01-10.wav\n", "03-01-03-02-01-02-10.wav\n", "03-01-04-01-02-02-10.wav\n", "03-01-04-01-01-01-10.wav\n", "03-01-04-02-01-01-10.wav\n", "03-01-03-02-02-01-10.wav\n", "03-01-04-02-01-02-10.wav\n", "03-01-03-02-02-02-10.wav\n", "03-01-04-01-01-02-10.wav\n", "03-01-04-02-02-01-10.wav\n", "03-01-04-02-02-02-10.wav\n", "03-01-05-01-02-01-10.wav\n", "03-01-05-01-01-01-10.wav\n", "03-01-05-01-02-02-10.wav\n", "03-01-05-01-01-02-10.wav\n", "03-01-05-02-02-01-10.wav\n", "03-01-05-02-01-01-10.wav\n", "03-01-05-02-02-02-10.wav\n", "03-01-05-02-01-02-10.wav\n", "03-01-06-01-01-01-10.wav\n", "03-01-06-01-02-02-10.wav\n", "03-01-06-01-01-02-10.wav\n", "03-01-06-01-02-01-10.wav\n", "03-01-06-02-01-01-10.wav\n", "03-01-06-02-01-02-10.wav\n", "03-01-06-02-02-01-10.wav\n", "03-01-07-01-02-01-10.wav\n", "03-01-07-01-01-02-10.wav\n", "03-01-07-01-01-01-10.wav\n", "03-01-06-02-02-02-10.wav\n", "03-01-07-02-01-01-10.wav\n", "03-01-07-01-02-02-10.wav\n", "03-01-07-02-01-02-10.wav\n", "03-01-07-02-02-01-10.wav\n", "03-01-08-01-01-01-10.wav\n", "03-01-08-01-01-02-10.wav\n", "03-01-07-02-02-02-10.wav\n", "03-01-08-01-02-01-10.wav\n", "03-01-08-01-02-02-10.wav\n", "03-01-08-02-01-01-10.wav\n", "03-02-01-01-01-01-10.wav\n", "03-01-08-02-02-02-10.wav\n", "03-01-08-02-01-02-10.wav\n", "03-01-08-02-02-01-10.wav\n", "03-02-01-01-01-02-10.wav\n", "03-02-01-01-02-01-10.wav\n", "03-02-01-01-02-02-10.wav\n", "03-02-02-01-01-01-10.wav\n", "03-02-02-01-01-02-10.wav\n", "03-02-02-01-02-01-10.wav\n", "03-02-02-02-02-01-10.wav\n", "03-02-02-02-01-01-10.wav\n", "03-02-02-01-02-02-10.wav\n", "03-02-03-01-01-01-10.wav\n", "03-02-02-02-02-02-10.wav\n", "03-02-02-02-01-02-10.wav\n", "03-02-03-01-01-02-10.wav\n", "03-02-03-01-02-01-10.wav\n", "03-02-03-01-02-02-10.wav\n", "03-02-04-01-01-01-10.wav\n", "03-02-03-02-01-01-10.wav\n", "03-02-04-01-02-01-10.wav\n", "03-02-04-01-01-02-10.wav\n", "03-02-03-02-02-01-10.wav\n", "03-02-03-02-01-02-10.wav\n", "03-02-04-01-02-02-10.wav\n", "03-02-03-02-02-02-10.wav\n", "03-02-04-02-01-01-10.wav\n", "03-02-05-01-01-01-10.wav\n", "03-02-04-02-01-02-10.wav\n", "03-02-05-01-01-02-10.wav\n", "03-02-04-02-02-02-10.wav\n", "03-02-04-02-02-01-10.wav\n", "03-02-05-02-01-01-10.wav\n", "03-02-05-01-02-02-10.wav\n", "03-02-05-01-02-01-10.wav\n", "03-02-05-02-01-02-10.wav\n", "03-02-05-02-02-01-10.wav\n", "03-02-05-02-02-02-10.wav\n", "03-02-06-01-01-01-10.wav\n", "03-02-06-01-02-01-10.wav\n", "03-02-06-01-01-02-10.wav\n", "03-02-06-02-01-01-10.wav\n", "03-02-06-01-02-02-10.wav\n", "03-02-06-02-01-02-10.wav\n", "03-02-06-02-02-02-10.wav\n", "03-02-06-02-02-01-10.wav\n", "03-01-02-01-01-01-07.wav\n", "03-01-01-01-01-02-07.wav\n", "03-01-01-01-01-01-07.wav\n", "03-01-02-01-02-01-07.wav\n", "03-01-02-01-01-02-07.wav\n", "03-01-01-01-02-01-07.wav\n", "03-01-01-01-02-02-07.wav\n", "03-01-02-01-02-02-07.wav\n", "03-01-02-02-01-01-07.wav\n", "03-01-03-01-02-01-07.wav\n", "03-01-02-02-01-02-07.wav\n", "03-01-03-01-02-02-07.wav\n", "03-01-03-02-01-01-07.wav\n", "03-01-02-02-02-02-07.wav\n", "03-01-03-01-01-02-07.wav\n", "03-01-03-01-01-01-07.wav\n", "03-01-03-02-01-02-07.wav\n", "03-01-02-02-02-01-07.wav\n", "03-01-03-02-02-01-07.wav\n", "03-01-03-02-02-02-07.wav\n", "03-01-04-01-01-01-07.wav\n", "03-01-04-01-01-02-07.wav\n", "03-01-04-01-02-01-07.wav\n", "03-01-04-01-02-02-07.wav\n", "03-01-04-02-01-01-07.wav\n", "03-01-04-02-02-01-07.wav\n", "03-01-05-01-01-01-07.wav\n", "03-01-04-02-01-02-07.wav\n", "03-01-05-01-01-02-07.wav\n", "03-01-04-02-02-02-07.wav\n", "03-01-05-01-02-01-07.wav\n", "03-01-05-01-02-02-07.wav\n", "03-01-05-02-01-01-07.wav\n", "03-01-05-02-01-02-07.wav\n", "03-01-06-01-02-01-07.wav\n", "03-01-05-02-02-01-07.wav\n", "03-01-06-01-02-02-07.wav\n", "03-01-06-01-01-02-07.wav\n", "03-01-06-01-01-01-07.wav\n", "03-01-06-02-01-01-07.wav\n", "03-01-05-02-02-02-07.wav\n", "03-01-06-02-01-02-07.wav\n", "03-01-06-02-02-01-07.wav\n", "03-01-06-02-02-02-07.wav\n", "03-01-07-01-02-01-07.wav\n", "03-01-07-01-01-01-07.wav\n", "03-01-07-02-01-01-07.wav\n", "03-01-07-01-01-02-07.wav\n", "03-01-07-01-02-02-07.wav\n", "03-01-08-01-01-01-07.wav\n", "03-01-08-01-02-01-07.wav\n", "03-01-08-01-01-02-07.wav\n", "03-01-08-02-01-01-07.wav\n", "03-01-08-01-02-02-07.wav\n", "03-01-08-02-01-02-07.wav\n", "03-01-08-02-02-01-07.wav\n", "03-01-08-02-02-02-07.wav\n", "03-02-01-01-01-01-07.wav\n", "03-02-01-01-01-02-07.wav\n", "03-02-01-01-02-02-07.wav\n", "03-02-01-01-02-01-07.wav\n", "03-01-07-02-02-02-07.wav\n", "03-01-07-02-02-01-07.wav\n", "03-01-07-02-01-02-07.wav\n", "03-02-02-01-01-01-07.wav\n", "03-02-02-01-01-02-07.wav\n", "03-02-03-01-01-01-07.wav\n", "03-02-02-01-02-01-07.wav\n", "03-02-02-02-02-01-07.wav\n", "03-02-02-01-02-02-07.wav\n", "03-02-02-02-02-02-07.wav\n", "03-02-02-02-01-02-07.wav\n", "03-02-03-01-01-02-07.wav\n", "03-02-02-02-01-01-07.wav\n", "03-02-03-01-02-01-07.wav\n", "03-02-03-01-02-02-07.wav\n", "03-02-03-02-02-01-07.wav\n", "03-02-03-02-01-01-07.wav\n", "03-02-03-02-02-02-07.wav\n", "03-02-03-02-01-02-07.wav\n", "03-02-04-01-01-01-07.wav\n", "03-02-04-01-02-02-07.wav\n", "03-02-04-02-01-01-07.wav\n", "03-02-04-01-01-02-07.wav\n", "03-02-04-02-01-02-07.wav\n", "03-02-04-01-02-01-07.wav\n", "03-02-04-02-02-01-07.wav\n", "03-02-04-02-02-02-07.wav\n", "03-02-05-01-01-01-07.wav\n", "03-02-05-01-02-01-07.wav\n", "03-02-05-01-01-02-07.wav\n", "03-02-06-01-01-01-07.wav\n", "03-02-05-01-02-02-07.wav\n", "03-02-05-02-02-01-07.wav\n", "03-02-05-02-01-01-07.wav\n", "03-02-05-02-02-02-07.wav\n", "03-02-06-01-01-02-07.wav\n", "03-02-06-02-01-01-07.wav\n", "03-02-05-02-01-02-07.wav\n", "03-02-06-02-01-02-07.wav\n", "03-02-06-01-02-01-07.wav\n", "03-02-06-01-02-02-07.wav\n", "03-02-06-02-02-01-07.wav\n", "03-02-06-02-02-02-07.wav\n", "03-01-01-01-01-01-09.wav\n", "03-01-01-01-01-02-09.wav\n", "03-01-01-01-02-01-09.wav\n", "03-01-01-01-02-02-09.wav\n", "03-01-02-01-01-01-09.wav\n", "03-01-02-01-01-02-09.wav\n", "03-01-02-01-02-01-09.wav\n", "03-01-02-02-02-01-09.wav\n", "03-01-02-01-02-02-09.wav\n", "03-01-02-02-01-02-09.wav\n", "03-01-02-02-01-01-09.wav\n", "03-01-03-01-01-02-09.wav\n", "03-01-03-01-01-01-09.wav\n", "03-01-02-02-02-02-09.wav\n", "03-01-03-01-02-01-09.wav\n", "03-01-03-02-01-01-09.wav\n", "03-01-03-01-02-02-09.wav\n", "03-01-03-02-02-01-09.wav\n", "03-01-04-01-01-01-09.wav\n", "03-01-03-02-01-02-09.wav\n", "03-01-03-02-02-02-09.wav\n", "03-01-04-01-02-01-09.wav\n", "03-01-04-01-01-02-09.wav\n", "03-01-04-01-02-02-09.wav\n", "03-01-04-02-01-01-09.wav\n", "03-01-04-02-01-02-09.wav\n", "03-01-05-01-01-02-09.wav\n", "03-01-05-01-02-01-09.wav\n", "03-01-05-01-02-02-09.wav\n", "03-01-05-01-01-01-09.wav\n", "03-01-04-02-02-01-09.wav\n", "03-01-04-02-02-02-09.wav\n", "03-01-05-02-01-01-09.wav\n", "03-01-05-02-01-02-09.wav\n", "03-01-05-02-02-02-09.wav\n", "03-01-06-01-02-01-09.wav\n", "03-01-06-02-01-01-09.wav\n", "03-01-05-02-02-01-09.wav\n", "03-01-06-02-01-02-09.wav\n", "03-01-06-01-01-01-09.wav\n", "03-01-06-01-01-02-09.wav\n", "03-01-06-02-02-01-09.wav\n", "03-01-06-01-02-02-09.wav\n", "03-01-06-02-02-02-09.wav\n", "03-01-07-01-01-01-09.wav\n", "03-01-07-02-02-01-09.wav\n", "03-01-07-01-01-02-09.wav\n", "03-01-07-02-02-02-09.wav\n", "03-01-07-01-02-01-09.wav\n", "03-01-08-01-01-01-09.wav\n", "03-01-07-01-02-02-09.wav\n", "03-01-08-01-01-02-09.wav\n", "03-01-07-02-01-02-09.wav\n", "03-01-07-02-01-01-09.wav\n", "03-01-08-01-02-01-09.wav\n", "03-01-08-01-02-02-09.wav\n", "03-01-08-02-02-01-09.wav\n", "03-01-08-02-01-02-09.wav\n", "03-01-08-02-01-01-09.wav\n", "03-01-08-02-02-02-09.wav\n", "03-02-01-01-01-01-09.wav\n", "03-02-01-01-02-01-09.wav\n", "03-02-01-01-01-02-09.wav\n", "03-02-02-01-01-01-09.wav\n", "03-02-01-01-02-02-09.wav\n", "03-02-02-01-01-02-09.wav\n", "03-02-02-01-02-01-09.wav\n", "03-02-02-01-02-02-09.wav\n", "03-02-02-02-01-01-09.wav\n", "03-02-02-02-01-02-09.wav\n", "03-02-02-02-02-01-09.wav\n", "03-02-03-01-02-01-09.wav\n", "03-02-03-01-01-01-09.wav\n", "03-02-03-01-02-02-09.wav\n", "03-02-03-01-01-02-09.wav\n", "03-02-03-02-01-01-09.wav\n", "03-02-02-02-02-02-09.wav\n", "03-02-03-02-01-02-09.wav\n", "03-02-03-02-02-01-09.wav\n", "03-02-04-02-02-02-09.wav\n", "03-02-03-02-02-02-09.wav\n", "03-02-04-02-01-01-09.wav\n", "03-02-04-01-01-01-09.wav\n", "03-02-04-02-02-01-09.wav\n", "03-02-04-02-01-02-09.wav\n", "03-02-04-01-02-02-09.wav\n", "03-02-04-01-02-01-09.wav\n", "03-02-04-01-01-02-09.wav\n", "03-02-05-01-01-01-09.wav\n", "03-02-05-01-02-01-09.wav\n", "03-02-05-01-01-02-09.wav\n", "03-02-05-01-02-02-09.wav\n", "03-02-05-02-01-01-09.wav\n", "03-02-05-02-02-01-09.wav\n", "03-02-05-02-01-02-09.wav\n", "03-02-06-01-01-01-09.wav\n", "03-02-06-01-02-01-09.wav\n", "03-02-06-01-01-02-09.wav\n", "03-02-05-02-02-02-09.wav\n", "03-02-06-01-02-02-09.wav\n", "03-02-06-02-01-02-09.wav\n", "03-02-06-02-01-01-09.wav\n", "03-02-06-02-02-01-09.wav\n", "03-02-06-02-02-02-09.wav\n", "03-01-01-01-02-02-08.wav\n", "03-01-02-01-01-01-08.wav\n", "03-01-01-01-01-01-08.wav\n", "03-01-01-01-01-02-08.wav\n", "03-01-01-01-02-01-08.wav\n", "03-01-02-01-02-01-08.wav\n", "03-01-02-01-01-02-08.wav\n", "03-01-02-01-02-02-08.wav\n", "03-01-02-02-01-01-08.wav\n", "03-01-02-02-01-02-08.wav\n", "03-01-03-01-02-01-08.wav\n", "03-01-02-02-02-01-08.wav\n", "03-01-03-01-02-02-08.wav\n", "03-01-03-01-01-01-08.wav\n", "03-01-02-02-02-02-08.wav\n", "03-01-03-02-01-01-08.wav\n", "03-01-03-01-01-02-08.wav\n", "03-01-03-02-01-02-08.wav\n", "03-01-03-02-02-01-08.wav\n", "03-01-03-02-02-02-08.wav\n", "03-01-04-01-01-01-08.wav\n", "03-01-04-01-02-02-08.wav\n", "03-01-04-01-02-01-08.wav\n", "03-01-04-01-01-02-08.wav\n", "03-01-04-02-01-01-08.wav\n", "03-01-04-02-01-02-08.wav\n", "03-01-04-02-02-01-08.wav\n", "03-01-04-02-02-02-08.wav\n", "03-01-05-01-01-01-08.wav\n", "03-01-05-01-02-01-08.wav\n", "03-01-05-01-01-02-08.wav\n", "03-01-05-02-02-01-08.wav\n", "03-01-05-02-01-01-08.wav\n", "03-01-05-01-02-02-08.wav\n", "03-01-05-02-02-02-08.wav\n", "03-01-05-02-01-02-08.wav\n", "03-01-06-01-01-02-08.wav\n", "03-01-06-01-01-01-08.wav\n", "03-01-06-01-02-01-08.wav\n", "03-01-06-01-02-02-08.wav\n", "03-01-07-01-01-01-08.wav\n", "03-01-06-02-01-01-08.wav\n", "03-01-07-01-01-02-08.wav\n", "03-01-06-02-01-02-08.wav\n", "03-01-07-01-02-01-08.wav\n", "03-01-07-01-02-02-08.wav\n", "03-01-06-02-02-01-08.wav\n", "03-01-06-02-02-02-08.wav\n", "03-01-07-02-01-01-08.wav\n", "03-01-08-01-01-01-08.wav\n", "03-01-07-02-01-02-08.wav\n", "03-01-08-01-01-02-08.wav\n", "03-01-07-02-02-01-08.wav\n", "03-01-07-02-02-02-08.wav\n", "03-01-08-02-01-01-08.wav\n", "03-01-08-01-02-02-08.wav\n", "03-01-08-01-02-01-08.wav\n", "03-01-08-02-01-02-08.wav\n", "03-01-08-02-02-01-08.wav\n", "03-01-08-02-02-02-08.wav\n", "03-02-01-01-01-01-08.wav\n", "03-02-01-01-02-01-08.wav\n", "03-02-01-01-02-02-08.wav\n", "03-02-01-01-01-02-08.wav\n", "03-02-02-01-01-01-08.wav\n", "03-02-02-01-02-01-08.wav\n", "03-02-02-01-01-02-08.wav\n", "03-02-02-01-02-02-08.wav\n", "03-02-02-02-01-01-08.wav\n", "03-02-02-02-01-02-08.wav\n", "03-02-02-02-02-01-08.wav\n", "03-02-03-01-01-01-08.wav\n", "03-02-02-02-02-02-08.wav\n", "03-02-03-01-01-02-08.wav\n", "03-02-03-01-02-02-08.wav\n", "03-02-03-01-02-01-08.wav\n", "03-02-03-02-02-01-08.wav\n", "03-02-03-02-01-01-08.wav\n", "03-02-03-02-01-02-08.wav\n", "03-02-04-01-01-01-08.wav\n", "03-02-03-02-02-02-08.wav\n", "03-02-04-01-01-02-08.wav\n", "03-02-04-01-02-01-08.wav\n", "03-02-04-01-02-02-08.wav\n", "03-02-04-02-01-01-08.wav\n", "03-02-05-01-02-02-08.wav\n", "03-02-04-02-01-02-08.wav\n", "03-02-04-02-02-01-08.wav\n", "03-02-05-01-01-02-08.wav\n", "03-02-05-01-01-01-08.wav\n", "03-02-05-01-02-01-08.wav\n", "03-02-04-02-02-02-08.wav\n", "03-02-06-01-01-01-08.wav\n", "03-02-05-02-01-02-08.wav\n", "03-02-06-01-01-02-08.wav\n", "03-02-06-01-02-01-08.wav\n", "03-02-05-02-02-01-08.wav\n", "03-02-05-02-02-02-08.wav\n", "03-02-06-02-01-01-08.wav\n", "03-02-06-01-02-02-08.wav\n", "03-02-06-02-01-02-08.wav\n", "03-02-05-02-01-01-08.wav\n", "03-02-06-02-02-01-08.wav\n", "03-02-06-02-02-02-08.wav\n", "03-01-01-01-01-01-05.wav\n", "03-01-02-01-01-02-05.wav\n", "03-01-01-01-01-02-05.wav\n", "03-01-02-01-02-01-05.wav\n", "03-01-01-01-02-01-05.wav\n", "03-01-02-02-01-01-05.wav\n", "03-01-01-01-02-02-05.wav\n", "03-01-02-01-02-02-05.wav\n", "03-01-02-01-01-01-05.wav\n", "03-01-02-02-01-02-05.wav\n", "03-01-03-01-01-02-05.wav\n", "03-01-02-02-02-01-05.wav\n", "03-01-03-01-02-02-05.wav\n", "03-01-02-02-02-02-05.wav\n", "03-01-03-01-01-01-05.wav\n", "03-01-03-02-01-01-05.wav\n", "03-01-03-01-02-01-05.wav\n", "03-01-03-02-01-02-05.wav\n", "03-01-03-02-02-01-05.wav\n", "03-01-03-02-02-02-05.wav\n", "03-01-04-01-01-01-05.wav\n", "03-01-04-01-01-02-05.wav\n", "03-01-04-01-02-02-05.wav\n", "03-01-04-02-01-01-05.wav\n", "03-01-04-02-01-02-05.wav\n", "03-01-05-01-01-02-05.wav\n", "03-01-04-02-02-01-05.wav\n", "03-01-05-01-02-01-05.wav\n", "03-01-04-02-02-02-05.wav\n", "03-01-05-02-01-01-05.wav\n", "03-01-05-01-01-01-05.wav\n", "03-01-05-01-02-02-05.wav\n", "03-01-05-02-01-02-05.wav\n", "03-01-06-01-02-02-05.wav\n", "03-01-05-02-02-01-05.wav\n", "03-01-06-02-01-01-05.wav\n", "03-01-05-02-02-02-05.wav\n", "03-01-06-02-01-02-05.wav\n", "03-01-06-01-01-02-05.wav\n", "03-01-06-02-02-01-05.wav\n", "03-01-06-01-02-01-05.wav\n", "03-01-06-01-01-01-05.wav\n", "03-01-06-02-02-02-05.wav\n", "03-01-07-01-01-01-05.wav\n", "03-01-07-01-01-02-05.wav\n", "03-01-07-01-02-02-05.wav\n", "03-01-07-01-02-01-05.wav\n", "03-01-07-02-02-02-05.wav\n", "03-01-07-02-02-01-05.wav\n", "03-01-07-02-01-01-05.wav\n", "03-01-08-01-01-01-05.wav\n", "03-01-07-02-01-02-05.wav\n", "03-01-08-01-01-02-05.wav\n", "03-01-08-02-01-02-05.wav\n", "03-01-08-01-02-01-05.wav\n", "03-01-08-01-02-02-05.wav\n", "03-01-08-02-02-01-05.wav\n", "03-01-08-02-01-01-05.wav\n", "03-01-08-02-02-02-05.wav\n", "03-02-01-01-01-01-05.wav\n", "03-02-01-01-01-02-05.wav\n", "03-02-01-01-02-01-05.wav\n", "03-02-01-01-02-02-05.wav\n", "03-02-02-01-01-01-05.wav\n", "03-02-02-01-01-02-05.wav\n", "03-02-02-01-02-01-05.wav\n", "03-02-02-01-02-02-05.wav\n", "03-02-02-02-01-01-05.wav\n", "03-02-03-01-01-02-05.wav\n", "03-02-02-02-01-02-05.wav\n", "03-02-02-02-02-01-05.wav\n", "03-02-03-01-02-01-05.wav\n", "03-02-02-02-02-02-05.wav\n", "03-02-03-01-02-02-05.wav\n", "03-02-03-01-01-01-05.wav\n", "03-02-03-02-01-01-05.wav\n", "03-02-03-02-01-02-05.wav\n", "03-02-04-01-02-02-05.wav\n", "03-02-03-02-02-01-05.wav\n", "03-02-04-02-01-01-05.wav\n", "03-02-03-02-02-02-05.wav\n", "03-02-04-02-01-02-05.wav\n", "03-02-04-01-01-01-05.wav\n", "03-02-04-02-02-01-05.wav\n", "03-02-04-01-01-02-05.wav\n", "03-02-04-01-02-01-05.wav\n", "03-02-04-02-02-02-05.wav\n", "03-02-05-01-01-01-05.wav\n", "03-02-05-01-02-02-05.wav\n", "03-02-05-01-01-02-05.wav\n", "03-02-05-02-01-01-05.wav\n", "03-02-05-01-02-01-05.wav\n", "03-02-05-02-01-02-05.wav\n", "03-02-05-02-02-02-05.wav\n", "03-02-05-02-02-01-05.wav\n", "03-02-06-01-01-01-05.wav\n", "03-02-06-01-02-02-05.wav\n", "03-02-06-01-01-02-05.wav\n", "03-02-06-02-01-01-05.wav\n", "03-02-06-01-02-01-05.wav\n", "03-02-06-02-01-02-05.wav\n", "03-02-06-02-02-01-05.wav\n", "03-02-06-02-02-02-05.wav\n", "03-01-01-01-02-01-14.wav\n", "03-01-01-01-01-02-14.wav\n", "03-01-01-01-01-01-14.wav\n", "03-01-01-01-02-02-14.wav\n", "03-01-02-01-01-01-14.wav\n", "03-01-02-01-02-01-14.wav\n", "03-01-02-01-01-02-14.wav\n", "03-01-02-01-02-02-14.wav\n", "03-01-02-02-01-01-14.wav\n", "03-01-02-02-01-02-14.wav\n", "03-01-02-02-02-01-14.wav\n", "03-01-03-01-02-01-14.wav\n", "03-01-02-02-02-02-14.wav\n", "03-01-03-01-01-02-14.wav\n", "03-01-03-01-01-01-14.wav\n", "03-01-03-01-02-02-14.wav\n", "03-01-03-02-01-02-14.wav\n", "03-01-03-02-01-01-14.wav\n", "03-01-03-02-02-01-14.wav\n", "03-01-03-02-02-02-14.wav\n", "03-01-04-02-01-01-14.wav\n", "03-01-04-01-01-02-14.wav\n", "03-01-04-01-01-01-14.wav\n", "03-01-04-02-01-02-14.wav\n", "03-01-04-01-02-01-14.wav\n", "03-01-04-01-02-02-14.wav\n", "03-01-04-02-02-02-14.wav\n", "03-01-04-02-02-01-14.wav\n", "03-01-05-01-01-01-14.wav\n", "03-01-05-01-01-02-14.wav\n", "03-01-05-02-02-01-14.wav\n", "03-01-05-01-02-01-14.wav\n", "03-01-05-01-02-02-14.wav\n", "03-01-06-01-01-01-14.wav\n", "03-01-06-01-01-02-14.wav\n", "03-01-05-02-01-01-14.wav\n", "03-01-05-02-02-02-14.wav\n", "03-01-05-02-01-02-14.wav\n", "03-01-06-01-02-01-14.wav\n", "03-01-06-01-02-02-14.wav\n", "03-01-06-02-02-02-14.wav\n", "03-01-06-02-01-01-14.wav\n", "03-01-06-02-02-01-14.wav\n", "03-01-06-02-01-02-14.wav\n", "03-01-07-01-02-01-14.wav\n", "03-01-07-01-01-02-14.wav\n", "03-01-07-01-01-01-14.wav\n", "03-01-07-01-02-02-14.wav\n", "03-01-07-02-01-01-14.wav\n", "03-01-07-02-02-01-14.wav\n", "03-01-07-02-01-02-14.wav\n", "03-01-08-01-01-01-14.wav\n", "03-01-07-02-02-02-14.wav\n", "03-01-08-01-01-02-14.wav\n", "03-01-08-01-02-01-14.wav\n", "03-01-08-02-02-01-14.wav\n", "03-01-08-02-01-01-14.wav\n", "03-01-08-02-01-02-14.wav\n", "03-01-08-01-02-02-14.wav\n", "03-02-01-01-01-01-14.wav\n", "03-01-08-02-02-02-14.wav\n", "03-02-01-01-01-02-14.wav\n", "03-02-01-01-02-01-14.wav\n", "03-02-02-01-01-01-14.wav\n", "03-02-01-01-02-02-14.wav\n", "03-02-02-02-01-01-14.wav\n", "03-02-02-01-01-02-14.wav\n", "03-02-02-01-02-02-14.wav\n", "03-02-02-02-01-02-14.wav\n", "03-02-02-01-02-01-14.wav\n", "03-02-02-02-02-02-14.wav\n", "03-02-02-02-02-01-14.wav\n", "03-02-03-01-01-01-14.wav\n", "03-02-03-02-02-01-14.wav\n", "03-02-03-01-01-02-14.wav\n", "03-02-03-02-02-02-14.wav\n", "03-02-04-01-01-01-14.wav\n", "03-02-03-01-02-01-14.wav\n", "03-02-04-01-01-02-14.wav\n", "03-02-03-01-02-02-14.wav\n", "03-02-03-02-01-02-14.wav\n", "03-02-03-02-01-01-14.wav\n", "03-02-04-01-02-01-14.wav\n", "03-02-04-01-02-02-14.wav\n", "03-02-04-02-01-01-14.wav\n", "03-02-04-02-02-01-14.wav\n", "03-02-04-02-01-02-14.wav\n", "03-02-04-02-02-02-14.wav\n", "03-02-05-01-01-01-14.wav\n", "03-02-05-01-01-02-14.wav\n", "03-02-05-02-01-01-14.wav\n", "03-02-05-01-02-01-14.wav\n", "03-02-05-02-01-02-14.wav\n", "03-02-05-01-02-02-14.wav\n", "03-02-05-02-02-01-14.wav\n", "03-02-05-02-02-02-14.wav\n", "03-02-06-01-01-02-14.wav\n", "03-02-06-01-02-01-14.wav\n", "03-02-06-02-02-01-14.wav\n", "03-02-06-01-01-01-14.wav\n", "03-02-06-02-02-02-14.wav\n", "03-02-06-02-01-02-14.wav\n", "03-02-06-01-02-02-14.wav\n", "03-02-06-02-01-01-14.wav\n", "03-01-01-01-01-01-11.wav\n", "03-01-01-01-01-02-11.wav\n", "03-01-01-01-02-02-11.wav\n", "03-01-01-01-02-01-11.wav\n", "03-01-02-01-01-01-11.wav\n", "03-01-02-01-02-01-11.wav\n", "03-01-02-01-01-02-11.wav\n", "03-01-02-02-01-01-11.wav\n", "03-01-02-01-02-02-11.wav\n", "03-01-02-02-01-02-11.wav\n", "03-01-02-02-02-01-11.wav\n", "03-01-02-02-02-02-11.wav\n", "03-01-03-01-01-01-11.wav\n", "03-01-03-02-01-01-11.wav\n", "03-01-03-01-01-02-11.wav\n", "03-01-03-01-02-01-11.wav\n", "03-01-03-01-02-02-11.wav\n", "03-01-04-01-01-01-11.wav\n", "03-01-04-01-01-02-11.wav\n", "03-01-04-02-01-02-11.wav\n", "03-01-04-02-01-01-11.wav\n", "03-01-04-01-02-02-11.wav\n", "03-01-04-01-02-01-11.wav\n", "03-01-03-02-01-02-11.wav\n", "03-01-03-02-02-01-11.wav\n", "03-01-03-02-02-02-11.wav\n", "03-01-04-02-02-01-11.wav\n", "03-01-05-01-01-01-11.wav\n", "03-01-04-02-02-02-11.wav\n", "03-01-05-01-02-01-11.wav\n", "03-01-05-01-01-02-11.wav\n", "03-01-05-02-02-01-11.wav\n", "03-01-05-01-02-02-11.wav\n", "03-01-05-02-01-01-11.wav\n", "03-01-05-02-01-02-11.wav\n", "03-01-05-02-02-02-11.wav\n", "03-01-06-01-01-01-11.wav\n", "03-01-06-01-01-02-11.wav\n", "03-01-06-01-02-01-11.wav\n", "03-01-06-02-02-01-11.wav\n", "03-01-06-01-02-02-11.wav\n", "03-01-06-02-02-02-11.wav\n", "03-01-06-02-01-01-11.wav\n", "03-01-06-02-01-02-11.wav\n", "03-01-07-01-01-02-11.wav\n", "03-01-07-01-01-01-11.wav\n", "03-01-07-01-02-01-11.wav\n", "03-01-08-01-01-01-11.wav\n", "03-01-07-01-02-02-11.wav\n", "03-01-07-02-01-01-11.wav\n", "03-01-08-01-02-01-11.wav\n", "03-01-08-01-01-02-11.wav\n", "03-01-07-02-01-02-11.wav\n", "03-01-07-02-02-02-11.wav\n", "03-01-08-01-02-02-11.wav\n", "03-01-07-02-02-01-11.wav\n", "03-01-08-02-01-01-11.wav\n", "03-02-01-01-01-01-11.wav\n", "03-01-08-02-02-01-11.wav\n", "03-01-08-02-01-02-11.wav\n", "03-02-01-01-01-02-11.wav\n", "03-02-01-01-02-01-11.wav\n", "03-01-08-02-02-02-11.wav\n", "03-02-01-01-02-02-11.wav\n", "03-02-02-01-01-01-11.wav\n", "03-02-02-01-01-02-11.wav\n", "03-02-02-01-02-01-11.wav\n", "03-02-02-01-02-02-11.wav\n", "03-02-03-01-01-01-11.wav\n", "03-02-02-02-01-01-11.wav\n", "03-02-03-01-01-02-11.wav\n", "03-02-02-02-01-02-11.wav\n", "03-02-02-02-02-01-11.wav\n", "03-02-02-02-02-02-11.wav\n", "03-02-03-01-02-01-11.wav\n", "03-02-03-01-02-02-11.wav\n", "03-02-03-02-01-01-11.wav\n", "03-02-03-02-01-02-11.wav\n", "03-02-03-02-02-01-11.wav\n", "03-02-03-02-02-02-11.wav\n", "03-02-04-01-01-01-11.wav\n", "03-02-04-01-01-02-11.wav\n", "03-02-04-01-02-01-11.wav\n", "03-02-05-01-01-01-11.wav\n", "03-02-05-01-01-02-11.wav\n", "03-02-04-01-02-02-11.wav\n", "03-02-04-02-01-02-11.wav\n", "03-02-04-02-01-01-11.wav\n", "03-02-04-02-02-02-11.wav\n", "03-02-04-02-02-01-11.wav\n", "03-02-05-01-02-01-11.wav\n", "03-02-06-01-01-01-11.wav\n", "03-02-05-01-02-02-11.wav\n", "03-02-05-02-01-01-11.wav\n", "03-02-06-01-01-02-11.wav\n", "03-02-05-02-02-02-11.wav\n", "03-02-05-02-01-02-11.wav\n", "03-02-05-02-02-01-11.wav\n", "03-02-06-01-02-02-11.wav\n", "03-02-06-01-02-01-11.wav\n", "03-02-06-02-01-01-11.wav\n", "03-02-06-02-02-01-11.wav\n", "03-02-06-02-01-02-11.wav\n", "03-02-06-02-02-02-11.wav\n", "03-01-02-01-01-01-06.wav\n", "03-01-02-01-01-02-06.wav\n", "03-01-01-01-02-01-06.wav\n", "03-01-02-01-02-01-06.wav\n", "03-01-01-01-02-02-06.wav\n", "03-01-01-01-01-01-06.wav\n", "03-01-01-01-01-02-06.wav\n", "03-01-02-02-01-01-06.wav\n", "03-01-02-02-01-02-06.wav\n", "03-01-03-01-01-01-06.wav\n", "03-01-02-02-02-02-06.wav\n", "03-01-02-02-02-01-06.wav\n", "03-01-03-01-01-02-06.wav\n", "03-01-03-01-02-01-06.wav\n", "03-01-03-01-02-02-06.wav\n", "03-01-03-02-01-01-06.wav\n", "03-01-03-02-01-02-06.wav\n", "03-01-03-02-02-02-06.wav\n", "03-01-03-02-02-01-06.wav\n", "03-01-04-01-01-01-06.wav\n", "03-01-04-01-01-02-06.wav\n", "03-01-04-01-02-01-06.wav\n", "03-01-04-02-01-01-06.wav\n", "03-01-04-01-02-02-06.wav\n", "03-01-04-02-01-02-06.wav\n", "03-01-05-01-01-02-06.wav\n", "03-01-04-02-02-02-06.wav\n", "03-01-05-01-01-01-06.wav\n", "03-01-05-01-02-01-06.wav\n", "03-01-05-01-02-02-06.wav\n", "03-01-04-02-02-01-06.wav\n", "03-01-05-02-01-01-06.wav\n", "03-01-06-01-01-02-06.wav\n", "03-01-05-02-01-02-06.wav\n", "03-01-06-01-01-01-06.wav\n", "03-01-05-02-02-01-06.wav\n", "03-01-05-02-02-02-06.wav\n", "03-01-06-01-02-01-06.wav\n", "03-01-06-02-01-01-06.wav\n", "03-01-06-01-02-02-06.wav\n", "03-01-06-02-02-02-06.wav\n", "03-01-06-02-02-01-06.wav\n", "03-01-06-02-01-02-06.wav\n", "03-01-07-01-01-01-06.wav\n", "03-01-07-01-01-02-06.wav\n", "03-01-07-01-02-01-06.wav\n", "03-01-07-02-01-01-06.wav\n", "03-01-07-02-01-02-06.wav\n", "03-01-07-01-02-02-06.wav\n", "03-01-07-02-02-01-06.wav\n", "03-01-07-02-02-02-06.wav\n", "03-01-08-01-01-02-06.wav\n", "03-01-08-01-02-02-06.wav\n", "03-01-08-01-01-01-06.wav\n", "03-01-08-01-02-01-06.wav\n", "03-01-08-02-01-01-06.wav\n", "03-01-08-02-01-02-06.wav\n", "03-01-08-02-02-01-06.wav\n", "03-01-08-02-02-02-06.wav\n", "03-02-01-01-01-02-06.wav\n", "03-02-01-01-01-01-06.wav\n", "03-02-01-01-02-01-06.wav\n", "03-02-01-01-02-02-06.wav\n", "03-02-02-01-01-01-06.wav\n", "03-02-02-01-01-02-06.wav\n", "03-02-02-01-02-02-06.wav\n", "03-02-02-01-02-01-06.wav\n", "03-02-02-02-01-01-06.wav\n", "03-02-02-02-02-01-06.wav\n", "03-02-02-02-01-02-06.wav\n", "03-02-02-02-02-02-06.wav\n", "03-02-03-01-01-02-06.wav\n", "03-02-03-01-01-01-06.wav\n", "03-02-03-01-02-01-06.wav\n", "03-02-03-01-02-02-06.wav\n", "03-02-04-01-01-01-06.wav\n", "03-02-04-01-01-02-06.wav\n", "03-02-03-02-01-01-06.wav\n", "03-02-03-02-01-02-06.wav\n", "03-02-04-01-02-01-06.wav\n", "03-02-03-02-02-01-06.wav\n", "03-02-04-01-02-02-06.wav\n", "03-02-03-02-02-02-06.wav\n", "03-01-02-01-02-02-06.wav\n", "03-02-05-01-01-01-06.wav\n", "03-02-04-02-01-02-06.wav\n", "03-02-05-01-01-02-06.wav\n", "03-02-04-02-02-01-06.wav\n", "03-02-05-01-02-01-06.wav\n", "03-02-04-02-01-01-06.wav\n", "03-02-04-02-02-02-06.wav\n", "03-02-05-01-02-02-06.wav\n", "03-02-05-02-01-01-06.wav\n", "03-02-05-02-01-02-06.wav\n", "03-02-05-02-02-01-06.wav\n", "03-02-06-01-02-01-06.wav\n", "03-02-06-01-01-02-06.wav\n", "03-02-06-02-01-01-06.wav\n", "03-02-06-02-02-01-06.wav\n", "03-02-06-01-02-02-06.wav\n", "03-02-06-02-01-02-06.wav\n", "03-02-05-02-02-02-06.wav\n", "03-02-06-01-01-01-06.wav\n", "03-02-06-02-02-02-06.wav\n", "03-01-01-01-01-01-12.wav\n", "03-01-01-01-01-02-12.wav\n", "03-01-01-01-02-01-12.wav\n", "03-01-02-01-01-01-12.wav\n", "03-01-01-01-02-02-12.wav\n", "03-01-02-01-02-01-12.wav\n", "03-01-02-01-01-02-12.wav\n", "03-01-02-01-02-02-12.wav\n", "03-01-02-02-01-01-12.wav\n", "03-01-02-02-01-02-12.wav\n", "03-01-02-02-02-01-12.wav\n", "03-01-02-02-02-02-12.wav\n", "03-01-03-01-01-01-12.wav\n", "03-01-03-01-02-01-12.wav\n", "03-01-03-01-01-02-12.wav\n", "03-01-03-01-02-02-12.wav\n", "03-01-03-02-01-01-12.wav\n", "03-01-03-02-02-01-12.wav\n", "03-01-03-02-01-02-12.wav\n", "03-01-04-01-01-01-12.wav\n", "03-01-04-01-02-01-12.wav\n", "03-01-04-01-01-02-12.wav\n", "03-01-04-01-02-02-12.wav\n", "03-01-03-02-02-02-12.wav\n", "03-01-04-02-01-01-12.wav\n", "03-01-04-02-01-02-12.wav\n", "03-01-05-01-01-01-12.wav\n", "03-01-04-02-02-01-12.wav\n", "03-01-05-02-01-01-12.wav\n", "03-01-04-02-02-02-12.wav\n", "03-01-05-02-01-02-12.wav\n", "03-01-05-01-01-02-12.wav\n", "03-01-05-02-02-01-12.wav\n", "03-01-05-01-02-01-12.wav\n", "03-01-05-02-02-02-12.wav\n", "03-01-05-01-02-02-12.wav\n", "03-01-06-01-01-01-12.wav\n", "03-01-06-01-01-02-12.wav\n", "03-01-06-02-01-01-12.wav\n", "03-01-06-01-02-01-12.wav\n", "03-01-06-02-01-02-12.wav\n", "03-01-06-01-02-02-12.wav\n", "03-01-07-01-01-01-12.wav\n", "03-01-06-02-02-01-12.wav\n", "03-01-06-02-02-02-12.wav\n", "03-01-07-01-01-02-12.wav\n", "03-01-07-01-02-01-12.wav\n", "03-01-07-02-01-01-12.wav\n", "03-01-07-01-02-02-12.wav\n", "03-01-07-02-02-01-12.wav\n", "03-01-07-02-01-02-12.wav\n", "03-01-08-01-01-01-12.wav\n", "03-01-07-02-02-02-12.wav\n", "03-01-08-01-02-01-12.wav\n", "03-01-08-01-01-02-12.wav\n", "03-01-08-02-01-01-12.wav\n", "03-01-08-01-02-02-12.wav\n", "03-01-08-02-02-01-12.wav\n", "03-01-08-02-01-02-12.wav\n", "03-01-08-02-02-02-12.wav\n", "03-02-01-01-01-01-12.wav\n", "03-02-01-01-02-01-12.wav\n", "03-02-01-01-01-02-12.wav\n", "03-02-02-01-02-01-12.wav\n", "03-02-02-01-01-01-12.wav\n", "03-02-01-01-02-02-12.wav\n", "03-02-02-01-02-02-12.wav\n", "03-02-02-02-01-01-12.wav\n", "03-02-02-02-01-02-12.wav\n", "03-02-02-01-01-02-12.wav\n", "03-02-03-02-01-01-12.wav\n", "03-02-02-02-02-01-12.wav\n", "03-02-02-02-02-02-12.wav\n", "03-02-03-02-01-02-12.wav\n", "03-02-03-01-01-01-12.wav\n", "03-02-03-01-02-01-12.wav\n", "03-02-03-01-02-02-12.wav\n", "03-02-03-01-01-02-12.wav\n", "03-02-03-02-02-01-12.wav\n", "03-02-03-02-02-02-12.wav\n", "03-02-04-01-01-01-12.wav\n", "03-02-04-02-02-01-12.wav\n", "03-02-04-01-01-02-12.wav\n", "03-02-04-02-02-02-12.wav\n", "03-02-04-01-02-01-12.wav\n", "03-02-05-01-01-01-12.wav\n", "03-02-04-01-02-02-12.wav\n", "03-02-05-01-01-02-12.wav\n", "03-02-04-02-01-01-12.wav\n", "03-02-04-02-01-02-12.wav\n", "03-02-05-01-02-01-12.wav\n", "03-02-05-01-02-02-12.wav\n", "03-02-05-02-01-01-12.wav\n", "03-02-06-01-01-01-12.wav\n", "03-02-06-01-01-02-12.wav\n", "03-02-05-02-02-01-12.wav\n", "03-02-05-02-01-02-12.wav\n", "03-02-05-02-02-02-12.wav\n", "03-02-06-01-02-01-12.wav\n", "03-02-06-01-02-02-12.wav\n", "03-02-06-02-01-01-12.wav\n", "03-02-06-02-02-01-12.wav\n", "03-02-06-02-01-02-12.wav\n", "03-02-06-02-02-02-12.wav\n", "03-01-01-01-01-02-13.wav\n", "03-01-01-01-01-01-13.wav\n", "03-01-01-01-02-01-13.wav\n", "03-01-02-02-02-02-13.wav\n", "03-01-01-01-02-02-13.wav\n", "03-01-02-02-01-01-13.wav\n", "03-01-02-01-02-02-13.wav\n", "03-01-02-01-01-01-13.wav\n", "03-01-02-02-02-01-13.wav\n", "03-01-02-02-01-02-13.wav\n", "03-01-02-01-01-02-13.wav\n", "03-01-02-01-02-01-13.wav\n", "03-01-03-01-01-01-13.wav\n", "03-01-03-02-02-02-13.wav\n", "03-01-03-01-02-02-13.wav\n", "03-01-03-01-02-01-13.wav\n", "03-01-03-01-01-02-13.wav\n", "03-01-04-01-01-02-13.wav\n", "03-01-03-02-01-02-13.wav\n", "03-01-03-02-02-01-13.wav\n", "03-01-03-02-01-01-13.wav\n", "03-01-04-01-01-01-13.wav\n", "03-01-04-01-02-01-13.wav\n", "03-01-04-01-02-02-13.wav\n", "03-01-04-02-02-01-13.wav\n", "03-01-04-02-01-01-13.wav\n", "03-01-05-01-01-01-13.wav\n", "03-01-05-01-01-02-13.wav\n", "03-01-04-02-02-02-13.wav\n", "03-01-04-02-01-02-13.wav\n", "03-01-05-01-02-02-13.wav\n", "03-01-05-01-02-01-13.wav\n", "03-01-05-02-01-01-13.wav\n", "03-01-05-02-02-01-13.wav\n", "03-01-06-01-01-01-13.wav\n", "03-01-05-02-01-02-13.wav\n", "03-01-05-02-02-02-13.wav\n", "03-01-06-01-01-02-13.wav\n", "03-01-06-01-02-02-13.wav\n", "03-01-06-02-01-01-13.wav\n", "03-01-06-01-02-01-13.wav\n", "03-01-06-02-02-01-13.wav\n", "03-01-06-02-01-02-13.wav\n", "03-01-06-02-02-02-13.wav\n", "03-01-07-01-01-01-13.wav\n", "03-01-07-01-01-02-13.wav\n", "03-01-07-01-02-01-13.wav\n", "03-01-08-01-01-01-13.wav\n", "03-01-07-02-01-01-13.wav\n", "03-01-07-01-02-02-13.wav\n", "03-01-08-01-02-01-13.wav\n", "03-01-08-01-01-02-13.wav\n", "03-01-07-02-02-01-13.wav\n", "03-01-07-02-01-02-13.wav\n", "03-01-07-02-02-02-13.wav\n", "03-01-08-01-02-02-13.wav\n", "03-01-08-02-01-01-13.wav\n", "03-01-08-02-01-02-13.wav\n", "03-02-01-01-02-01-13.wav\n", "03-01-08-02-02-01-13.wav\n", "03-01-08-02-02-02-13.wav\n", "03-02-01-01-02-02-13.wav\n", "03-02-02-01-01-01-13.wav\n", "03-02-01-01-01-01-13.wav\n", "03-02-01-01-01-02-13.wav\n", "03-02-02-01-01-02-13.wav\n", "03-02-02-01-02-01-13.wav\n", "03-02-03-01-01-01-13.wav\n", "03-02-02-01-02-02-13.wav\n", "03-02-03-01-01-02-13.wav\n", "03-02-02-02-01-01-13.wav\n", "03-02-03-01-02-02-13.wav\n", "03-02-03-01-02-01-13.wav\n", "03-02-02-02-01-02-13.wav\n", "03-02-02-02-02-01-13.wav\n", "03-02-02-02-02-02-13.wav\n", "03-02-03-02-01-01-13.wav\n", "03-02-03-02-01-02-13.wav\n", "03-02-03-02-02-02-13.wav\n", "03-02-04-01-01-01-13.wav\n", "03-02-04-02-01-01-13.wav\n", "03-02-04-01-02-01-13.wav\n", "03-02-04-01-01-02-13.wav\n", "03-02-04-01-02-02-13.wav\n", "03-02-04-02-01-02-13.wav\n", "03-02-03-02-02-01-13.wav\n", "03-02-04-02-02-01-13.wav\n", "03-02-05-02-01-01-13.wav\n", "03-02-04-02-02-02-13.wav\n", "03-02-05-02-02-01-13.wav\n", "03-02-05-01-01-01-13.wav\n", "03-02-05-01-01-02-13.wav\n", "03-02-05-02-01-02-13.wav\n", "03-02-05-02-02-02-13.wav\n", "03-02-05-01-02-02-13.wav\n", "03-02-05-01-02-01-13.wav\n", "03-02-06-01-01-01-13.wav\n", "03-02-06-01-01-02-13.wav\n", "03-02-06-02-01-01-13.wav\n", "03-02-06-01-02-01-13.wav\n", "03-02-06-02-01-02-13.wav\n", "03-02-06-02-02-01-13.wav\n", "03-02-06-01-02-02-13.wav\n", "03-02-06-02-02-02-13.wav\n", "03-01-02-01-01-02-02.wav\n", "03-01-01-01-01-01-02.wav\n", "03-01-02-01-02-01-02.wav\n", "03-01-01-01-01-02-02.wav\n", "03-01-02-01-02-02-02.wav\n", "03-01-01-01-02-01-02.wav\n", "03-01-02-02-01-01-02.wav\n", "03-01-02-01-01-01-02.wav\n", "03-01-01-01-02-02-02.wav\n", "03-01-02-02-01-02-02.wav\n", "03-01-03-01-01-02-02.wav\n", "03-01-02-02-02-01-02.wav\n", "03-01-03-01-02-01-02.wav\n", "03-01-02-02-02-02-02.wav\n", "03-01-03-01-01-01-02.wav\n", "03-01-03-02-01-02-02.wav\n", "03-01-03-01-02-02-02.wav\n", "03-01-03-02-01-01-02.wav\n", "03-01-03-02-02-01-02.wav\n", "03-01-03-02-02-02-02.wav\n", "03-01-04-01-01-01-02.wav\n", "03-01-04-01-01-02-02.wav\n", "03-01-04-01-02-01-02.wav\n", "03-01-04-01-02-02-02.wav\n", "03-01-04-02-01-02-02.wav\n", "03-01-04-02-01-01-02.wav\n", "03-01-04-02-02-01-02.wav\n", "03-01-05-01-01-01-02.wav\n", "03-01-05-01-01-02-02.wav\n", "03-01-04-02-02-02-02.wav\n", "03-01-05-01-02-01-02.wav\n", "03-01-05-01-02-02-02.wav\n", "03-01-05-02-01-01-02.wav\n", "03-01-05-02-01-02-02.wav\n", "03-01-05-02-02-01-02.wav\n", "03-01-05-02-02-02-02.wav\n", "03-01-06-01-02-01-02.wav\n", "03-01-06-01-01-02-02.wav\n", "03-01-06-01-01-01-02.wav\n", "03-01-06-01-02-02-02.wav\n", "03-01-06-02-01-01-02.wav\n", "03-01-06-02-01-02-02.wav\n", "03-01-07-01-01-01-02.wav\n", "03-01-06-02-02-01-02.wav\n", "03-01-06-02-02-02-02.wav\n", "03-01-07-01-01-02-02.wav\n", "03-01-07-01-02-01-02.wav\n", "03-01-07-02-02-02-02.wav\n", "03-01-07-01-02-02-02.wav\n", "03-01-08-01-01-01-02.wav\n", "03-01-07-02-01-01-02.wav\n", "03-01-08-01-01-02-02.wav\n", "03-01-07-02-01-02-02.wav\n", "03-01-08-01-02-01-02.wav\n", "03-01-07-02-02-01-02.wav\n", "03-01-08-01-02-02-02.wav\n", "03-01-08-02-02-02-02.wav\n", "03-01-08-02-01-01-02.wav\n", "03-02-01-01-01-01-02.wav\n", "03-01-08-02-01-02-02.wav\n", "03-02-01-01-01-02-02.wav\n", "03-01-08-02-02-01-02.wav\n", "03-02-01-01-02-01-02.wav\n", "03-02-02-01-01-01-02.wav\n", "03-02-01-01-02-02-02.wav\n", "03-02-02-01-01-02-02.wav\n", "03-02-02-01-02-02-02.wav\n", "03-02-02-01-02-01-02.wav\n", "03-02-02-02-01-02-02.wav\n", "03-02-02-02-01-01-02.wav\n", "03-02-02-02-02-01-02.wav\n", "03-02-03-01-01-01-02.wav\n", "03-02-02-02-02-02-02.wav\n", "03-02-03-01-01-02-02.wav\n", "03-02-03-01-02-01-02.wav\n", "03-02-03-01-02-02-02.wav\n", "03-02-03-02-01-01-02.wav\n", "03-02-03-02-01-02-02.wav\n", "03-02-03-02-02-01-02.wav\n", "03-02-03-02-02-02-02.wav\n", "03-02-04-01-02-02-02.wav\n", "03-02-04-01-01-02-02.wav\n", "03-02-04-01-02-01-02.wav\n", "03-02-04-01-01-01-02.wav\n", "03-02-04-02-01-02-02.wav\n", "03-02-04-02-01-01-02.wav\n", "03-02-04-02-02-02-02.wav\n", "03-02-04-02-02-01-02.wav\n", "03-02-05-01-01-01-02.wav\n", "03-02-05-01-01-02-02.wav\n", "03-02-05-01-02-02-02.wav\n", "03-02-05-01-02-01-02.wav\n", "03-02-05-02-02-02-02.wav\n", "03-02-05-02-01-01-02.wav\n", "03-02-05-02-02-01-02.wav\n", "03-02-06-01-01-01-02.wav\n", "03-02-05-02-01-02-02.wav\n", "03-02-06-01-02-01-02.wav\n", "03-02-06-01-01-02-02.wav\n", "03-02-06-01-02-02-02.wav\n", "03-02-06-02-01-01-02.wav\n", "03-02-06-02-01-02-02.wav\n", "03-02-06-02-02-01-02.wav\n", "03-02-06-02-02-02-02.wav\n", "03-01-01-01-01-02-01.wav\n", "03-01-01-01-02-01-01.wav\n", "03-01-01-01-02-02-01.wav\n", "03-01-01-01-01-01-01.wav\n", "03-01-02-01-01-01-01.wav\n", "03-01-02-01-02-01-01.wav\n", "03-01-02-01-02-02-01.wav\n", "03-01-02-02-01-02-01.wav\n", "03-01-02-02-01-01-01.wav\n", "03-01-03-01-01-02-01.wav\n", "03-01-02-01-01-02-01.wav\n", "03-01-02-02-02-01-01.wav\n", "03-01-02-02-02-02-01.wav\n", "03-01-03-01-02-01-01.wav\n", "03-01-03-01-01-01-01.wav\n", "03-01-03-01-02-02-01.wav\n", "03-01-03-02-01-02-01.wav\n", "03-01-03-02-01-01-01.wav\n", "03-01-03-02-02-02-01.wav\n", "03-01-03-02-02-01-01.wav\n", "03-01-04-01-01-02-01.wav\n", "03-01-04-01-01-01-01.wav\n", "03-01-04-01-02-02-01.wav\n", "03-01-04-01-02-01-01.wav\n", "03-01-04-02-01-02-01.wav\n", "03-01-04-02-01-01-01.wav\n", "03-01-04-02-02-02-01.wav\n", "03-01-04-02-02-01-01.wav\n", "03-01-05-01-01-01-01.wav\n", "03-01-05-01-01-02-01.wav\n", "03-01-05-01-02-02-01.wav\n", "03-01-05-01-02-01-01.wav\n", "03-01-06-01-01-01-01.wav\n", "03-01-05-02-01-01-01.wav\n", "03-01-05-02-01-02-01.wav\n", "03-01-05-02-02-01-01.wav\n", "03-01-06-01-01-02-01.wav\n", "03-01-06-01-02-01-01.wav\n", "03-01-05-02-02-02-01.wav\n", "03-01-06-01-02-02-01.wav\n", "03-01-07-01-01-02-01.wav\n", "03-01-06-02-01-01-01.wav\n", "03-01-07-01-02-01-01.wav\n", "03-01-06-02-01-02-01.wav\n", "03-01-07-01-02-02-01.wav\n", "03-01-06-02-02-01-01.wav\n", "03-01-06-02-02-02-01.wav\n", "03-01-07-01-01-01-01.wav\n", "03-01-07-02-01-01-01.wav\n", "03-01-07-02-01-02-01.wav\n", "03-01-08-02-01-01-01.wav\n", "03-01-07-02-02-01-01.wav\n", "03-01-08-02-02-01-01.wav\n", "03-01-08-02-01-02-01.wav\n", "03-01-07-02-02-02-01.wav\n", "03-01-08-01-01-02-01.wav\n", "03-01-08-01-02-02-01.wav\n", "03-01-08-01-01-01-01.wav\n", "03-01-08-01-02-01-01.wav\n", "03-02-01-01-02-01-01.wav\n", "03-02-01-01-01-01-01.wav\n", "03-01-08-02-02-02-01.wav\n", "03-02-01-01-02-02-01.wav\n", "03-02-02-01-01-01-01.wav\n", "03-02-02-01-02-02-01.wav\n", "03-02-01-01-01-02-01.wav\n", "03-02-02-01-01-02-01.wav\n", "03-02-02-02-01-01-01.wav\n", "03-02-02-01-02-01-01.wav\n", "03-02-02-02-01-02-01.wav\n", "03-02-02-02-02-01-01.wav\n", "03-02-02-02-02-02-01.wav\n", "03-02-03-01-01-02-01.wav\n", "03-02-03-01-01-01-01.wav\n", "03-02-03-01-02-02-01.wav\n", "03-02-03-01-02-01-01.wav\n", "03-02-03-02-02-01-01.wav\n", "03-02-03-02-01-02-01.wav\n", "03-02-03-02-01-01-01.wav\n", "03-02-03-02-02-02-01.wav\n", "03-02-04-01-01-01-01.wav\n", "03-02-04-01-01-02-01.wav\n", "03-02-04-01-02-01-01.wav\n", "03-02-04-02-01-02-01.wav\n", "03-02-04-02-02-01-01.wav\n", "03-02-05-01-01-02-01.wav\n", "03-02-05-01-01-01-01.wav\n", "03-02-04-01-02-02-01.wav\n", "03-02-05-01-02-02-01.wav\n", "03-02-05-01-02-01-01.wav\n", "03-02-04-02-02-02-01.wav\n", "03-02-04-02-01-01-01.wav\n", "03-02-05-02-01-01-01.wav\n", "03-02-05-02-01-02-01.wav\n", "03-02-06-02-02-01-01.wav\n", "03-02-05-02-02-01-01.wav\n", "03-02-06-01-02-02-01.wav\n", "03-02-06-02-01-01-01.wav\n", "03-02-05-02-02-02-01.wav\n", "03-02-06-02-01-02-01.wav\n", "03-02-06-01-01-02-01.wav\n", "03-02-06-01-01-01-01.wav\n", "03-02-06-01-02-01-01.wav\n", "03-02-06-02-02-02-01.wav\n", "03-01-01-01-02-02-04.wav\n", "03-01-02-01-01-01-04.wav\n", "03-01-01-01-01-01-04.wav\n", "03-01-02-01-01-02-04.wav\n", "03-01-01-01-02-01-04.wav\n", "03-01-02-01-02-01-04.wav\n", "03-01-01-01-01-02-04.wav\n", "03-01-02-01-02-02-04.wav\n", "03-01-02-02-01-01-04.wav\n", "03-01-02-02-01-02-04.wav\n", "03-01-03-01-01-02-04.wav\n", "03-01-02-02-02-01-04.wav\n", "03-01-03-01-02-01-04.wav\n", "03-01-02-02-02-02-04.wav\n", "03-01-03-01-02-02-04.wav\n", "03-01-03-01-01-01-04.wav\n", "03-01-03-02-01-01-04.wav\n", "03-01-03-02-01-02-04.wav\n", "03-01-04-01-02-02-04.wav\n", "03-01-03-02-02-01-04.wav\n", "03-01-04-02-01-01-04.wav\n", "03-01-03-02-02-02-04.wav\n", "03-01-04-02-01-02-04.wav\n", "03-01-04-01-01-02-04.wav\n", "03-01-04-02-02-01-04.wav\n", "03-01-04-01-01-01-04.wav\n", "03-01-04-01-02-01-04.wav\n", "03-01-04-02-02-02-04.wav\n", "03-01-05-01-01-01-04.wav\n", "03-01-05-01-01-02-04.wav\n", "03-01-05-01-02-02-04.wav\n", "03-01-05-01-02-01-04.wav\n", "03-01-05-02-01-01-04.wav\n", "03-01-05-02-01-02-04.wav\n", "03-01-05-02-02-02-04.wav\n", "03-01-06-01-01-01-04.wav\n", "03-01-05-02-02-01-04.wav\n", "03-01-06-01-01-02-04.wav\n", "03-01-06-01-02-02-04.wav\n", "03-01-06-01-02-01-04.wav\n", "03-01-06-02-01-01-04.wav\n", "03-01-06-02-01-02-04.wav\n", "03-01-06-02-02-01-04.wav\n", "03-01-06-02-02-02-04.wav\n", "03-01-07-01-02-02-04.wav\n", "03-01-07-01-01-02-04.wav\n", "03-01-07-01-01-01-04.wav\n", "03-01-07-01-02-01-04.wav\n", "03-01-07-02-01-01-04.wav\n", "03-01-07-02-01-02-04.wav\n", "03-01-07-02-02-01-04.wav\n", "03-01-07-02-02-02-04.wav\n", "03-01-08-01-01-01-04.wav\n", "03-01-08-01-01-02-04.wav\n", "03-01-08-02-01-02-04.wav\n", "03-01-08-02-02-01-04.wav\n", "03-01-08-01-02-01-04.wav\n", "03-01-08-02-01-01-04.wav\n", "03-01-08-01-02-02-04.wav\n", "03-02-01-01-01-01-04.wav\n", "03-01-08-02-02-02-04.wav\n", "03-02-01-01-01-02-04.wav\n", "03-02-02-01-02-02-04.wav\n", "03-02-01-01-02-01-04.wav\n", "03-02-02-02-01-01-04.wav\n", "03-02-01-01-02-02-04.wav\n", "03-02-02-01-01-02-04.wav\n", "03-02-02-01-01-01-04.wav\n", "03-02-02-01-02-01-04.wav\n", "03-02-02-02-01-02-04.wav\n", "03-02-02-02-02-01-04.wav\n", "03-02-02-02-02-02-04.wav\n", "03-02-03-02-01-02-04.wav\n", "03-02-03-01-01-01-04.wav\n", "03-02-03-02-02-01-04.wav\n", "03-02-03-01-01-02-04.wav\n", "03-02-03-02-02-02-04.wav\n", "03-02-03-01-02-01-04.wav\n", "03-02-04-01-01-01-04.wav\n", "03-02-03-01-02-02-04.wav\n", "03-02-03-02-01-01-04.wav\n", "03-02-04-01-01-02-04.wav\n", "03-02-04-02-01-02-04.wav\n", "03-02-04-01-02-01-04.wav\n", "03-02-04-02-02-01-04.wav\n", "03-02-04-01-02-02-04.wav\n", "03-02-04-02-02-02-04.wav\n", "03-02-04-02-01-01-04.wav\n", "03-02-05-01-01-01-04.wav\n", "03-02-05-01-01-02-04.wav\n", "03-02-05-01-02-01-04.wav\n", "03-02-05-01-02-02-04.wav\n", "03-02-05-02-01-01-04.wav\n", "03-02-05-02-01-02-04.wav\n", "03-02-06-01-01-01-04.wav\n", "03-02-05-02-02-01-04.wav\n", "03-02-05-02-02-02-04.wav\n", "03-02-06-01-01-02-04.wav\n", "03-02-06-01-02-01-04.wav\n", "03-02-06-02-01-02-04.wav\n", "03-02-06-01-02-02-04.wav\n", "03-02-06-02-02-01-04.wav\n", "03-02-06-02-01-01-04.wav\n", "03-02-06-02-02-02-04.wav\n", "03-01-01-01-01-01-03.wav\n", "03-01-01-01-01-02-03.wav\n", "03-01-01-01-02-01-03.wav\n", "03-01-01-01-02-02-03.wav\n", "03-01-02-01-02-02-03.wav\n", "03-01-02-01-01-01-03.wav\n", "03-01-02-02-01-01-03.wav\n", "03-01-02-02-01-02-03.wav\n", "03-01-02-01-01-02-03.wav\n", "03-01-02-02-02-01-03.wav\n", "03-01-02-01-02-01-03.wav\n", "03-01-02-02-02-02-03.wav\n", "03-01-03-01-02-02-03.wav\n", "03-01-03-01-01-01-03.wav\n", "03-01-03-02-01-01-03.wav\n", "03-01-03-01-01-02-03.wav\n", "03-01-03-02-02-01-03.wav\n", "03-01-03-01-02-01-03.wav\n", "03-01-03-02-02-02-03.wav\n", "03-01-03-02-01-02-03.wav\n", "03-01-04-01-01-01-03.wav\n", "03-01-04-01-01-02-03.wav\n", "03-01-04-02-01-01-03.wav\n", "03-01-04-01-02-02-03.wav\n", "03-01-04-01-02-01-03.wav\n", "03-01-04-02-01-02-03.wav\n", "03-01-04-02-02-01-03.wav\n", "03-01-04-02-02-02-03.wav\n", "03-01-05-01-01-01-03.wav\n", "03-01-05-01-02-02-03.wav\n", "03-01-05-01-01-02-03.wav\n", "03-01-05-02-01-01-03.wav\n", "03-01-05-01-02-01-03.wav\n", "03-01-05-02-01-02-03.wav\n", "03-01-05-02-02-01-03.wav\n", "03-01-06-01-01-01-03.wav\n", "03-01-06-02-01-02-03.wav\n", "03-01-06-01-01-02-03.wav\n", "03-01-06-02-02-01-03.wav\n", "03-01-06-01-02-02-03.wav\n", "03-01-06-02-02-02-03.wav\n", "03-01-05-02-02-02-03.wav\n", "03-01-07-01-01-01-03.wav\n", "03-01-06-01-02-01-03.wav\n", "03-01-06-02-01-01-03.wav\n", "03-01-07-01-01-02-03.wav\n", "03-01-07-01-02-01-03.wav\n", "03-01-07-02-01-02-03.wav\n", "03-01-08-01-01-02-03.wav\n", "03-01-07-01-02-02-03.wav\n", "03-01-08-01-02-01-03.wav\n", "03-01-07-02-01-01-03.wav\n", "03-01-07-02-02-01-03.wav\n", "03-01-07-02-02-02-03.wav\n", "03-01-08-01-01-01-03.wav\n", "03-01-08-01-02-02-03.wav\n", "03-01-08-02-01-01-03.wav\n", "03-01-08-02-01-02-03.wav\n", "03-01-08-02-02-02-03.wav\n", "03-01-08-02-02-01-03.wav\n", "03-02-01-01-01-01-03.wav\n", "03-02-01-01-01-02-03.wav\n", "03-02-01-01-02-02-03.wav\n", "03-02-02-01-02-01-03.wav\n", "03-02-01-01-02-01-03.wav\n", "03-02-02-01-01-02-03.wav\n", "03-02-02-01-01-01-03.wav\n", "03-02-02-01-02-02-03.wav\n", "03-02-02-02-01-01-03.wav\n", "03-02-02-02-01-02-03.wav\n", "03-02-02-02-02-01-03.wav\n", "03-02-03-01-02-02-03.wav\n", "03-02-02-02-02-02-03.wav\n", "03-02-03-02-01-01-03.wav\n", "03-02-03-01-01-01-03.wav\n", "03-02-03-02-01-02-03.wav\n", "03-02-03-01-02-01-03.wav\n", "03-02-03-02-02-01-03.wav\n", "03-02-03-01-01-02-03.wav\n", "03-02-03-02-02-02-03.wav\n", "03-02-04-01-01-01-03.wav\n", "03-02-04-01-02-02-03.wav\n", "03-02-04-01-01-02-03.wav\n", "03-02-04-02-01-01-03.wav\n", "03-02-04-01-02-01-03.wav\n", "03-02-04-02-01-02-03.wav\n", "03-02-04-02-02-01-03.wav\n", "03-02-04-02-02-02-03.wav\n", "03-02-05-01-01-02-03.wav\n", "03-02-05-01-01-01-03.wav\n", "03-02-05-02-01-01-03.wav\n", "03-02-05-01-02-01-03.wav\n", "03-02-05-01-02-02-03.wav\n", "03-02-05-02-01-02-03.wav\n", "03-02-05-02-02-01-03.wav\n", "03-02-05-02-02-02-03.wav\n", "03-02-06-01-02-02-03.wav\n", "03-02-06-01-01-01-03.wav\n", "03-02-06-02-01-01-03.wav\n", "03-02-06-01-01-02-03.wav\n", "03-02-06-01-02-01-03.wav\n", "03-02-06-02-01-02-03.wav\n", "03-02-06-02-02-01-03.wav\n", "03-02-06-02-02-02-03.wav\n", "[+] Number of training samples: 985\n", "[+] Number of testing samples: 329\n", "[+] Number of features: 180\n", "[*] Training the model...\n", "Accuracy: 82.07%\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "dPiCGtcBvp8x", "colab_type": "code", "outputId": "737b02a3-4dc2-4d95-dc0b-696b9c588f26", "colab": {"base_uri": "https://localhost:8080/", "height": 51}}, "source": ["model = MLPClassifier(**model_params)\n", "\n", "# train the model\n", "print(\"[*] Training the model...\")\n", "model.fit(X_train, y_train)\n", "\n", "# predict 25% of data to measure how good we are\n", "y_pred = model.predict(X_test)\n", "\n", "# calculate the accuracy\n", "accuracy = accuracy_score(y_true=y_test, y_pred=y_pred)\n", "\n", "print(\"Accuracy: {:.2f}%\".format(accuracy*100))"], "execution_count": 0, "outputs": [{"output_type": "stream", "text": ["[*] Training the model...\n", "Accuracy: 82.37%\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "xtOIZETWwmdG", "colab_type": "code", "outputId": "8b48c598-674a-48d6-c3d0-a4d0568451d8", "colab": {"base_uri": "https://localhost:8080/", "height": 272}}, "source": ["from sklearn.metrics import classification_report \n", "from sklearn.metrics import confusion_matrix \n", "print(classification_report(y_test,y_pred))\n", "print(confusion_matrix(y_test,y_pred))"], "execution_count": 0, "outputs": [{"output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "       angry       0.94      0.86      0.90        90\n", "       happy       0.81      0.80      0.80        94\n", "     neutral       0.70      0.73      0.71        44\n", "         sad       0.81      0.86      0.83       101\n", "\n", "    accuracy                           0.82       329\n", "   macro avg       0.81      0.81      0.81       329\n", "weighted avg       0.83      0.82      0.82       329\n", "\n", "[[77  9  2  2]\n", " [ 5 75  5  9]\n", " [ 0  2 32 10]\n", " [ 0  7  7 87]]\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "nCR_7QpHsHkF", "colab_type": "text"}, "source": ["**Second**"]}, {"cell_type": "code", "metadata": {"id": "nVJBOLmQr_RB", "colab_type": "code", "outputId": "ca1639ca-26fc-4394-8abf-c8239ae69d67", "colab": {"base_uri": "https://localhost:8080/", "height": 51}}, "source": ["m_params = {\n", "    'alpha': 0.01,\n", "    'batch_size': 200,\n", "    'epsilon': 1e-08, \n", "    'hidden_layer_sizes': (300,), \n", "    'learning_rate': 'adaptive', \n", "    'max_iter': 500, \n", "}\n", "# initialize Multi Layer Perceptron classifier\n", "# with best parameters ( so far )\n", "m1 = MLPClassifier(**m_params)\n", "\n", "# train the model\n", "print(\"[*] Training the model...\")\n", "m1.fit(X_train, y_train)\n", "\n", "# predict 25% of data to measure how good we are\n", "y_p = m1.predict(X_test)\n", "\n", "# calculate the accuracy\n", "accuracy = accuracy_score(y_true=y_test, y_pred=y_p)\n", "\n", "print(\"Accuracy: {:.2f}%\".format(accuracy*100))\n", "\n", "# now we save the model\n", "# make result directory if doesn't exist yet"], "execution_count": 0, "outputs": [{"output_type": "stream", "text": ["[*] Training the model...\n", "Accuracy: 80.24%\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "Mb3Dyz8Sy1eH", "colab_type": "code", "outputId": "33001957-9417-42c0-f703-9dd6717ad297", "colab": {"base_uri": "https://localhost:8080/", "height": 272}}, "source": ["from sklearn.metrics import classification_report \n", "from sklearn.metrics import confusion_matrix \n", "print(classification_report(y_test,y_p))\n", "print(confusion_matrix(y_test,y_p))"], "execution_count": 0, "outputs": [{"output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "       angry       0.93      0.89      0.91        90\n", "       happy       0.69      0.91      0.79        94\n", "     neutral       0.76      0.59      0.67        44\n", "         sad       0.86      0.71      0.78       101\n", "\n", "    accuracy                           0.80       329\n", "   macro avg       0.81      0.78      0.78       329\n", "weighted avg       0.82      0.80      0.80       329\n", "\n", "[[80  9  1  0]\n", " [ 4 86  1  3]\n", " [ 0  9 26  9]\n", " [ 2 21  6 72]]\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "bmnrmte120LG", "colab_type": "text"}, "source": ["**Third**"]}, {"cell_type": "code", "metadata": {"id": "QEz514j12ybq", "colab_type": "code", "outputId": "46132e53-8424-4d5b-d8ea-0ae9ec7ea0a1", "colab": {"base_uri": "https://localhost:8080/", "height": 51}}, "source": ["mn_params = {\n", "    'alpha': 0.01,\n", "    'batch_size': 256,\n", "    'epsilon': 1e-08, \n", "    'hidden_layer_sizes': (300,2), \n", "    'learning_rate': 'adaptive', \n", "    'max_iter': 500, \n", "}\n", "# initialize Multi Layer Perceptron classifier\n", "# with best parameters ( so far )\n", "m2 = MLPClassifier(**mn_params)\n", "\n", "# train the model\n", "print(\"[*] Training the model...\")\n", "m2.fit(X_train, y_train)\n", "\n", "# predict 25% of data to measure how good we are\n", "y_p2 = m2.predict(X_test)\n", "\n", "# calculate the accuracy\n", "accuracy = accuracy_score(y_true=y_test, y_pred=y_p2)\n", "\n", "print(\"Accuracy: {:.2f}%\".format(accuracy*100))"], "execution_count": 0, "outputs": [{"output_type": "stream", "text": ["[*] Training the model...\n", "Accuracy: 28.57%\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "9-OTZzDt29Qm", "colab_type": "code", "outputId": "4c886709-ff5e-4beb-c9ca-a41a63849901", "colab": {"base_uri": "https://localhost:8080/", "height": 326}}, "source": ["from sklearn.metrics import classification_report \n", "from sklearn.metrics import confusion_matrix \n", "print(classification_report(y_test,y_p2))\n", "print(confusion_matrix(y_test,y_p2))"], "execution_count": 0, "outputs": [{"output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "       angry       0.00      0.00      0.00        90\n", "       happy       0.29      1.00      0.44        94\n", "     neutral       0.00      0.00      0.00        44\n", "         sad       0.00      0.00      0.00       101\n", "\n", "    accuracy                           0.29       329\n", "   macro avg       0.07      0.25      0.11       329\n", "weighted avg       0.08      0.29      0.13       329\n", "\n", "[[  0  90   0   0]\n", " [  0  94   0   0]\n", " [  0  44   0   0]\n", " [  0 101   0   0]]\n"], "name": "stdout"}, {"output_type": "stream", "text": ["/usr/local/lib/python3.6/dist-packages/sklearn/metrics/_classification.py:1272: UndefinedMetricWarning: Precision and F-score are ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, msg_start, len(result))\n"], "name": "stderr"}]}, {"cell_type": "code", "metadata": {"id": "XcJQp0kDV3yG", "colab_type": "code", "colab": {}}, "source": ["import os\n", "import wave\n", "import pickle\n", "from sys import byteorder\n", "from array import array\n", "from struct import pack\n", "from sklearn.neural_network import MLPClassifier\n"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "5I6CDcGoCzG6", "colab_type": "code", "colab": {}}, "source": ["#print(\"Please talk\")\n", "filename = \"D:\\project_emotion/test2.wav\"# record the file (start talking)\n", "    #record_to_file(filename)\n", "    # extract features and reshape it\n", "features = extract_feature(filename, mfcc=True, chroma=True, mel=True).reshape(1, -1)\n", "    # predict\n", "result = model.predict(features)[0]\n", "    # show the result !\n", "print(\"result:\", result)"], "execution_count": 0, "outputs": []}]}