from PyQt5.QtWidgets import (QA<PERSON>lication, QMainWindow, QTextEdit, QStackedWidget, QWidget,
                             QVBoxLayout, QPushButton, QFrame, QLabel, QSizePolicy,
                             QHBoxLayout)
from PyQt5.QtGui import (QPainter, QColor, QTextCharFormat, QFont, QPixmap,
                         QTextBlockFormat, QPen)
from PyQt5.QtCore import Qt, QSize, QTimer
import sys
import os
import random
import math

# Add parent directory to path to import Data.DLLs
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from Data.DLLs import *

# Global variables
old_chat_message = ""

# --- Simple Voice Animation ---
class VoiceAnimation(QWidget):
    def __init__(self, parent=None, size=300):
        super().__init__(parent)
        self.setMinimumSize(size, size)
        self.setMaximumSize(size, size)

        # Animation parameters
        self.num_lines = 30
        self.base_radius = size // 2 - 20
        self.min_line_length = 5
        self.max_line_length = 25
        self.line_width = 2

        # Animation state
        self.mode = "idle"
        self.amplitudes = [self.min_line_length] * self.num_lines
        self.target_amplitudes = [self.min_line_length] * self.num_lines
        self.animation_speed = 0.2
        self.animation_counter = 0

        # Colors
        self.idle_color = QColor(0, 255, 0, 100)
        self.listening_color = QColor(0, 255, 100, 180)
        self.speaking_color = QColor(100, 255, 0, 200)
        self.current_color = self.idle_color

        # Start animation timer
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_animation)
        self.timer.start(50)  # 20 FPS

    def set_mode(self, mode):
        if self.mode != mode:
            self.mode = mode
            if mode == "idle":
                self.current_color = self.idle_color
            elif mode == "listening":
                self.current_color = self.listening_color
            elif mode == "speaking":
                self.current_color = self.speaking_color

    def update_animation(self):
        try:
            self.animation_counter += 1

            if self.mode == "idle":
                base_amplitude = self.min_line_length + (self.max_line_length - self.min_line_length) * 0.3
                pulse_factor = 0.1 * math.sin(0.05 * self.animation_counter)
                for i in range(self.num_lines):
                    self.target_amplitudes[i] = base_amplitude + pulse_factor * self.max_line_length

            elif self.mode == "listening":
                for i in range(self.num_lines):
                    angle = 2 * math.pi * i / self.num_lines
                    time_factor = 0.05 * self.animation_counter
                    wave = math.sin(angle * 3 + time_factor) * 0.5 + 0.5
                    self.target_amplitudes[i] = self.min_line_length + wave * (self.max_line_length - self.min_line_length)

            elif self.mode == "speaking":
                for i in range(self.num_lines):
                    if random.random() < 0.1:
                        rand_val = random.random()
                        self.target_amplitudes[i] = self.min_line_length + rand_val * (self.max_line_length - self.min_line_length)

            # Smooth animation
            for i in range(self.num_lines):
                self.amplitudes[i] += (self.target_amplitudes[i] - self.amplitudes[i]) * self.animation_speed

            if self.isVisible():
                self.update()
        except Exception as e:
            print(f"Animation error: {e}")

    def paintEvent(self, _):
        try:
            painter = QPainter(self)
            painter.setRenderHint(QPainter.Antialiasing)
            painter.fillRect(self.rect(), QColor(0, 0, 0, 0))

            if self.width() <= 0 or self.height() <= 0:
                return

            center_x = self.width() // 2
            center_y = self.height() // 2

            for i in range(min(self.num_lines, len(self.amplitudes))):
                try:
                    angle = 2 * math.pi * i / self.num_lines
                    inner_radius = max(0, self.base_radius - self.amplitudes[i])
                    outer_radius = max(0, self.base_radius + self.amplitudes[i])

                    start_x = center_x + inner_radius * math.cos(angle)
                    start_y = center_y + inner_radius * math.sin(angle)
                    end_x = center_x + outer_radius * math.cos(angle)
                    end_y = center_y + outer_radius * math.sin(angle)

                    pen = QPen(self.current_color)
                    pen.setWidth(self.line_width)
                    pen.setCapStyle(Qt.RoundCap)
                    painter.setPen(pen)

                    painter.drawLine(int(start_x), int(start_y), int(end_x), int(end_y))
                except Exception:
                    continue

        except Exception as e:
            print(f"Paint error: {e}")

# --- Utility Functions ---
def AnswerModifier(Answer):
    lines = Answer.split('\n')
    non_empty_lines = [line for line in lines if line.strip()]
    return '\n'.join(non_empty_lines)

def QueryModifier(Query):
    new_query = Query.lower().strip()
    query_words = new_query.split()
    question_words = ["how", "what", "who", "where", "when", "why", "which", "whose", "whom", "can you", "what is", "where's", "how's"]
    if any(word + " " in new_query for word in question_words):
        if query_words[-1][-1] in ['.', '?', '!']:
            new_query = new_query[:-1]
        else:
            new_query += "?"
    else:
        if query_words[-1][-1] in ['.', '?', '!']:
            new_query = new_query[:-1] + "."
        else:
            new_query += "."
    return new_query.capitalize()

def SetMicrophoneStatus(command):
    try:
        with open(os.path.join(TempDirectoryPath, "Mic.data"), "w", encoding='utf-8') as file:
            file.write(command)
    except Exception as e:
        print("Error writing Mic.data:", e)

def GetMicrophoneStatus():
    try:
        with open(os.path.join(TempDirectoryPath, "Mic.data"), "r", encoding='utf-8') as file:
            return file.read().strip()
    except Exception as e:
        print("Error reading Mic.data:", e)
        return ""

def SetAssistantStatus(Status):
    file_path = os.path.join(TempDirectoryPath, "Status.data")
    if not os.path.exists(TempDirectoryPath):
        os.makedirs(TempDirectoryPath)
    try:
        with open(file_path, "w", encoding="utf-8") as file:
            file.write(Status)
    except Exception as e:
        print("Error writing Status.data:", e)

def GetAssistantStatus():
    file_path = os.path.join(TempDirectoryPath, "Status.data")
    if not os.path.exists(TempDirectoryPath):
        os.makedirs(TempDirectoryPath)
    if not os.path.exists(file_path):
        SetAssistantStatus("")
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            return file.read().strip()
    except Exception as e:
        print("Error reading Status.data:", e)
        return ""

def MicButtonInitialed():
    SetMicrophoneStatus("True")

def MicButtonClosed():
    SetMicrophoneStatus("False")

def GraphicsPath(Filename):
    return os.path.join(GraphicsDirectoryPath, Filename)

def TempPath(Filename):
    return os.path.join(TempDirectoryPath, Filename)

def ShowTextToScreen(Text):
    try:
        with open(os.path.join(TempDirectoryPath, "Responses.data"), "w", encoding='utf-8') as file:
            file.write(Text)
    except Exception as e:
        print("Error writing Responses.data:", e)

# --- Simple Chat Section ---
class ChatSection(QWidget):
    def __init__(self):
        super(ChatSection, self).__init__()
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 80, 40, 100)
        layout.setSpacing(10)

        self.chat_text_edit = QTextEdit()
        self.chat_text_edit.setReadOnly(True)
        self.chat_text_edit.setTextInteractionFlags(Qt.NoTextInteraction)
        self.chat_text_edit.setFrameStyle(QFrame.NoFrame)
        layout.addWidget(self.chat_text_edit)

        # Simple styling
        self.setStyleSheet("""
            QWidget {
                background-color: #000000;
                color: #00FF00;
            }
        """)

        # Chat text styling
        self.chat_text_edit.setStyleSheet("""
            QTextEdit {
                background-color: #001100;
                color: #00FF00;
                border: 2px solid #00AA00;
                border-radius: 10px;
                padding: 15px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 14px;
            }
        """)

        layout.setSizeConstraint(QVBoxLayout.SetDefaultConstraint)
        layout.setStretch(1, 1)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        text_color = QColor(0, 255, 0)
        text_format = QTextCharFormat()
        text_format.setForeground(text_color)
        self.chat_text_edit.setCurrentCharFormat(text_format)

        # Create voice animation widget
        self.animation_widget = VoiceAnimation(size=200)
        self.animation_widget.setStyleSheet("border: none; background-color: transparent; margin-top: 100px;")
        layout.addWidget(self.animation_widget, alignment=Qt.AlignRight | Qt.AlignBottom)

        self.label = QLabel("")
        self.label.setStyleSheet("""
            color: #00FF00;
            font-size: 16px;
            margin-right: 120px;
            border: none;
            margin-top: 10px;
            font-family: 'Consolas', 'Courier New', monospace;
            font-weight: bold;
        """)
        self.label.setAlignment(Qt.AlignRight)
        layout.addWidget(self.label)

        font = QFont("Consolas", 13)
        font.setStyleHint(QFont.Monospace)
        self.chat_text_edit.setFont(font)

        self.timer = QTimer(self)
        self.timer.timeout.connect(self.loadMessages)
        self.timer.timeout.connect(self.SpeechRecogText)
        self.timer.start(100)

        self.chat_text_edit.viewport().installEventFilter(self)

    def loadMessages(self):
        global old_chat_message
        try:
            with open(TempPath("Responses.data"), "r", encoding='utf-8') as file:
                messages = file.read()
            if messages and messages != old_chat_message:
                self.addMessage(messages, color="White")
                old_chat_message = messages
        except Exception as e:
            print("Error loading messages:", e)

    def SpeechRecogText(self):
        try:
            with open(TempPath("Status.data"), "r", encoding='utf-8') as file:
                messages = file.read()
            self.label.setText(messages)
        except Exception as e:
            print("Error updating speech recognition text:", e)

    def addMessage(self, message, color):
        cursor = self.chat_text_edit.textCursor()
        fmt = QTextCharFormat()
        blockFmt = QTextBlockFormat()
        blockFmt.setTopMargin(10)
        blockFmt.setLeftMargin(10)
        fmt.setForeground(QColor(color))
        cursor.setCharFormat(fmt)
        cursor.setBlockFormat(blockFmt)
        cursor.insertText(message + "\n")
        self.chat_text_edit.setTextCursor(cursor)

# --- Simple Initial Screen ---
class InitialScreen(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        desktop = QApplication.desktop()
        screen_width = desktop.screenGeometry().width()
        screen_height = desktop.screenGeometry().height()

        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)

        # Create voice animation widget
        animation_widget = VoiceAnimation(size=280)
        animation_widget.setStyleSheet("border: none; background-color: transparent;")
        animation_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # Microphone button (reduced size)
        self.icon_label = QLabel()
        pixmap = QPixmap(GraphicsPath('Mic_on.png'))
        if pixmap.isNull():
            print("Warning: 'Mic_on.png' not found, using text placeholder")
            # Create a text-based microphone icon (smaller size)
            self.icon_label.setText("🎤")
            self.icon_label.setStyleSheet("""
                QLabel {
                    font-size: 32px;
                    color: #00FF00;
                    border: 2px solid #00FF00;
                    border-radius: 50px;
                    background-color: rgba(0, 255, 0, 20);
                    padding: 8px;
                }
                QLabel:hover {
                    background-color: rgba(0, 255, 0, 40);
                    border: 3px solid #00FF00;
                }
            """)
        else:
            new_pixmap = pixmap.scaled(50, 50)  # Reduced from 70x70
            self.icon_label.setPixmap(new_pixmap)
            # Simple mic button styling for image (smaller size)
            self.icon_label.setStyleSheet("""
                QLabel {
                    border: 2px solid #00FF00;
                    border-radius: 50px;
                    background-color: rgba(0, 255, 0, 20);
                    padding: 8px;
                }
                QLabel:hover {
                    background-color: rgba(0, 255, 0, 40);
                    border: 3px solid #00FF00;
                }
            """)
        self.icon_label.setFixedSize(100, 100)  # Reduced from 150x150
        self.icon_label.setAlignment(Qt.AlignCenter)

        # Set initial mic state
        self.toggled = False
        self.toggle_icon = self.__toggle_icon_impl
        self.icon_label.mousePressEvent = self.toggle_icon
        self.load_icon(GraphicsPath('Mic_on.png'), "🎤")
        print("🎤 GUI: Microphone button initialized as ACTIVE")

        # Status label
        self.label = QLabel("")
        self.label.setStyleSheet("""
            color: #00FF00;
            font-size: 18px;
            margin-bottom: 0;
            font-family: 'Consolas', 'Courier New', monospace;
            font-weight: bold;
            text-align: center;
            background-color: rgba(0, 255, 0, 10);
            border: 1px solid #00AA00;
            border-radius: 10px;
            padding: 10px;
        """)

        content_layout.addWidget(animation_widget, alignment=Qt.AlignCenter)
        content_layout.addSpacing(30)
        content_layout.addWidget(self.label, alignment=Qt.AlignCenter)
        content_layout.addSpacing(40)
        content_layout.addWidget(self.icon_label, alignment=Qt.AlignCenter)
        content_layout.setContentsMargins(0, 120, 0, 100)
        self.setLayout(content_layout)
        self.setFixedHeight(screen_height)
        self.setFixedWidth(screen_width)

        # Simple background
        self.setStyleSheet("""
            QWidget {
                background-color: #000000;
            }
        """)

        self.timer = QTimer(self)
        self.timer.timeout.connect(self.SpeechRecogText)
        self.timer.start(100)

    def SpeechRecogText(self):
        try:
            with open(TempPath("Status.data"), "r", encoding='utf-8') as file:
                messages = file.read()
            self.label.setText(messages)
        except Exception as e:
            print("Error in InitialScreen SpeechRecogText:", e)

    def __toggle_icon_impl(self, _=None):
        if not self.toggled:
            self.load_icon(GraphicsPath('Mic_on.png'), "🎤")
            MicButtonInitialed()
            print("Microphone activated")
        else:
            self.load_icon(GraphicsPath('Mic_off.png'), "🔇")
            MicButtonClosed()
            print("Microphone deactivated")
        self.toggled = not self.toggled

    def load_icon(self, path, fallback_text="🎤", width=50, height=50):
        pixmap = QPixmap(path)
        if pixmap.isNull():
            print(f"Warning: Icon at {path} not found, using text fallback: {fallback_text}")
            self.icon_label.setText(fallback_text)
            self.icon_label.setStyleSheet("""
                QLabel {
                    font-size: 32px;
                    color: #00FF00;
                    border: 2px solid #00FF00;
                    border-radius: 50px;
                    background-color: rgba(0, 255, 0, 20);
                    padding: 8px;
                }
                QLabel:hover {
                    background-color: rgba(0, 255, 0, 40);
                    border: 3px solid #00FF00;
                }
            """)
        else:
            new_pixmap = pixmap.scaled(width, height)
            self.icon_label.setPixmap(new_pixmap)
            self.icon_label.setText("")  # Clear any text
            self.icon_label.setStyleSheet("""
                QLabel {
                    border: 2px solid #00FF00;
                    border-radius: 50px;
                    background-color: rgba(0, 255, 0, 20);
                    padding: 8px;
                }
                QLabel:hover {
                    background-color: rgba(0, 255, 0, 40);
                    border: 3px solid #00FF00;
                }
            """)

# --- Simple Message Screen ---
class MessageScreen(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        desktop = QApplication.desktop()
        screen_width = desktop.screenGeometry().width()
        screen_height = desktop.screenGeometry().height()
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        chat_section = ChatSection()
        layout.addWidget(chat_section)
        self.setLayout(layout)

        # Simple background
        self.setStyleSheet("""
            QWidget {
                background-color: #000000;
            }
        """)
        self.setFixedHeight(screen_height)
        self.setFixedWidth(screen_width)

# --- Simple Main Window ---
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.initUI()

    def initUI(self):
        desktop = QApplication.desktop()
        screen_width = desktop.screenGeometry().width()
        screen_height = desktop.screenGeometry().height()

        # Create stacked widget for screens
        self.stacked_widget = QStackedWidget()

        # Create simple screens
        initial_screen = InitialScreen()
        message_screen = MessageScreen()
        self.stacked_widget.addWidget(initial_screen)
        self.stacked_widget.addWidget(message_screen)

        # Set as central widget
        self.setCentralWidget(self.stacked_widget)

        # Window properties
        self.setGeometry(100, 100, screen_width - 200, screen_height - 200)
        self.setMinimumSize(800, 600)

        # Simple styling
        self.setStyleSheet("""
            QMainWindow {
                background-color: #000000;
            }
        """)

        # Set window title
        self.setWindowTitle("Matrix AI Assistant")

        # Add navigation buttons
        self.create_navigation()

    def create_navigation(self):
        # Simple navigation with buttons
        nav_widget = QWidget()
        nav_layout = QHBoxLayout(nav_widget)

        home_button = QPushButton("HOME")
        home_button.setStyleSheet("""
            QPushButton {
                background-color: #003300;
                color: #00FF00;
                border: 1px solid #00AA00;
                border-radius: 5px;
                padding: 5px 15px;
                font-family: 'Consolas', monospace;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #005500;
                border: 1px solid #00FF00;
            }
        """)
        home_button.clicked.connect(lambda: self.stacked_widget.setCurrentIndex(0))

        chat_button = QPushButton("CHAT")
        chat_button.setStyleSheet("""
            QPushButton {
                background-color: #003300;
                color: #00FF00;
                border: 1px solid #00AA00;
                border-radius: 5px;
                padding: 5px 15px;
                font-family: 'Consolas', monospace;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #005500;
                border: 1px solid #00FF00;
            }
        """)
        chat_button.clicked.connect(lambda: self.stacked_widget.setCurrentIndex(1))

        # Center-align navigation buttons with proper spacing
        nav_layout.addStretch()  # Left stretch to center buttons
        nav_layout.addWidget(home_button)
        nav_layout.addSpacing(15)  # Space between buttons
        nav_layout.addWidget(chat_button)
        nav_layout.addStretch()  # Right stretch to center buttons

        # Add navigation to window
        nav_widget.setFixedHeight(40)
        nav_widget.setStyleSheet("background-color: #001100; border-bottom: 1px solid #00AA00;")

        # Create main widget with navigation
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        main_layout.addWidget(nav_widget)
        main_layout.addWidget(self.stacked_widget)

        self.setCentralWidget(main_widget)

# --- Startup coordination functions ---
def signal_gui_ready():
    """Signal that GUI is ready"""
    try:
        with open(TempPath('GUI_Ready.data'), 'w') as f:
            f.write('ready')
        print("✅ GUI ready signal sent")
    except Exception as e:
        print(f"Error signaling GUI ready: {e}")

def is_loading_complete():
    """Check if loading screen is complete"""
    try:
        return os.path.exists(TempPath('LoadingComplete.data'))
    except Exception:
        return False

def check_and_show_window_after_loading(window, timer):
    """Check if loading is complete and show the window"""
    if is_loading_complete():
        print("🎉 Loading screen complete! Showing main interface...")
        window.show()
        timer.stop()

        # Clean up coordination files
        try:
            loading_file = TempPath('LoadingComplete.data')
            if os.path.exists(loading_file):
                os.remove(loading_file)
            gui_ready_file = TempPath('GUI_Ready.data')
            if os.path.exists(gui_ready_file):
                os.remove(gui_ready_file)
            print("🧹 Startup coordination files cleaned up")
        except Exception as e:
            print(f"Error cleaning up startup files: {e}")

def GraphicalUserInterface():
    app = QApplication(sys.argv)
    window = MainWindow()

    # Start hidden during startup and loading screen
    window.hide()

    # Signal that GUI is ready (but hidden)
    signal_gui_ready()

    # Start a timer to check for loading screen completion
    loading_timer = QTimer()
    loading_timer.timeout.connect(lambda: check_and_show_window_after_loading(window, loading_timer))
    loading_timer.start(500)  # Check every 500ms

    print("🖥️ GUI initialized and hidden, waiting for loading screen to complete...")

    sys.exit(app.exec_())

if __name__ == "__main__":
    GraphicalUserInterface()
