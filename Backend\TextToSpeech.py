import pygame
import random
import asyncio
import edge_tts
import os
import threading
import uuid
import tempfile
import shutil
import time
from pathlib import Path
from typing import Optional, Tuple
import atexit
from dotenv import dotenv_values

# Load environment variables
env_vars = dotenv_values(".env")

# AI Voice Configuration - Heavy, Dangerous, Savage AI Persona
AI_VOICE_OPTIONS = [
    "en-US-SteffanNeural",      # Deep, authoritative male voice
    "en-US-DavisNeural",        # Strong, commanding male voice
    "en-US-JasonNeural",        # Powerful, intense male voice
    "en-GB-RyanNeural",         # British, sophisticated but menacing
    "en-AU-WilliamNeural",      # Australian, rugged and tough
]

# Select primary AI voice (can be overridden by .env)
AssistantVoice = env_vars.get("AssistantVoice", "en-US-SteffanNeural")

# Advanced AI Voice Parameters for Heavy, Dangerous Sound - OPTIMIZED FOR SPEED
AI_VOICE_SETTINGS = {
    "pitch": "-15Hz",
    "rate": "+35%",            # Increased from +15% to +35% for much faster speech
    "volume": "+10%",
}

# Thread-safe TTS file manager
class TTSFileManager:
    """
    Thread-safe file manager for TTS operations.
    Handles unique file creation, locking, and cleanup.
    """

    def __init__(self):
        self.lock = threading.RLock()
        self.active_files = {}  # Track active audio files by thread
        self.temp_dir = Path("Data/tts_temp")
        self.temp_dir.mkdir(exist_ok=True)
        self.cleanup_on_exit = True

        # Register cleanup on exit
        atexit.register(self.cleanup_all_files)

    def get_unique_audio_path(self, thread_id: Optional[str] = None) -> str:
        """Get a unique audio file path for the current operation."""
        with self.lock:
            if thread_id is None:
                thread_id = str(threading.current_thread().ident)

            # Generate unique filename
            unique_id = str(uuid.uuid4())[:8]
            filename = f"speech_{thread_id}_{unique_id}.mp3"
            file_path = self.temp_dir / filename

            # Store active file for this thread
            self.active_files[thread_id] = str(file_path)

            return str(file_path)

    def cleanup_file(self, file_path: str, thread_id: Optional[str] = None) -> bool:
        """Safely cleanup a specific audio file."""
        with self.lock:
            try:
                if thread_id is None:
                    thread_id = str(threading.current_thread().ident)

                # Remove from active files
                if thread_id in self.active_files:
                    del self.active_files[thread_id]

                # Remove file with retry logic
                return self._safe_remove_file(file_path)

            except Exception as e:
                print(f"⚠️ Error cleaning up TTS file {file_path}: {e}")
                return False

    def _safe_remove_file(self, file_path: str, max_retries: int = 5) -> bool:
        """Safely remove file with retry logic and pygame-specific handling."""
        for attempt in range(max_retries):
            try:
                if os.path.exists(file_path):
                    # For pygame files, we need to ensure mixer releases the file
                    if attempt > 0:
                        # Try to force pygame to release the file
                        try:
                            import pygame
                            if pygame.mixer.get_init() is not None:
                                pygame.mixer.music.stop()
                                pygame.mixer.music.unload()  # Try to unload if available
                        except:
                            pass

                        # Exponential backoff with longer delays for file locks
                        time.sleep(0.2 * (2 ** attempt))

                    os.remove(file_path)
                    return True
                return True  # File doesn't exist, consider it cleaned

            except PermissionError:
                if attempt == max_retries - 1:
                    # File is still locked, schedule for later cleanup
                    self._schedule_delayed_cleanup(file_path)
                    return False
                continue
            except Exception as e:
                if attempt == max_retries - 1:
                    print(f"⚠️ Unexpected error removing TTS file {file_path}: {e}")
                return False

        return False

    def _schedule_delayed_cleanup(self, file_path: str):
        """Schedule file for delayed cleanup when it's no longer locked."""
        def delayed_cleanup():
            time.sleep(2)  # Wait 2 seconds
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    print(f"✅ Delayed cleanup successful: {os.path.basename(file_path)}")
            except:
                pass  # Silent fail for delayed cleanup

        # Run delayed cleanup in background thread
        cleanup_thread = threading.Thread(target=delayed_cleanup, daemon=True)
        cleanup_thread.start()

    def cleanup_all_files(self):
        """Cleanup all temporary TTS files."""
        with self.lock:
            try:
                # Clean up tracked files
                for thread_id, file_path in list(self.active_files.items()):
                    self._safe_remove_file(file_path)

                self.active_files.clear()

                # Clean up temp directory
                if self.temp_dir.exists():
                    for file_path in self.temp_dir.glob("speech_*.mp3"):
                        self._safe_remove_file(str(file_path))

                print("✅ TTS temporary files cleaned up")

            except Exception as e:
                print(f"⚠️ Error during TTS cleanup: {e}")

    def get_active_file_count(self) -> int:
        """Get number of active TTS files."""
        with self.lock:
            return len(self.active_files)

# Global TTS file manager instance
tts_file_manager = TTSFileManager()

# Legacy path for backward compatibility (now unused)
AUDIO_FILE_PATH = r"Data\speech.mp3"

def enhance_ai_speech(text: str) -> str:
    """Enhance text for more AI-like, heavy, dangerous delivery."""
    # Remove symbols that shouldn't be spoken
    symbols_to_remove = ['*', ':', ';', '~', '#', '!']
    enhanced = text
    for symbol in symbols_to_remove:
        enhanced = enhanced.replace(symbol, '')

    # Add strategic pauses for more menacing effect
    enhanced = enhanced.replace(". ", "... ")  # Longer pauses between sentences
    enhanced = enhanced.replace(", ", ".. ")  # Pauses after commas for emphasis

    # Add emphasis to certain AI-related words
    ai_emphasis_words = {
        "Matrix AI": "Matrix... A.I.",
        "artificial intelligence": "artificial... intelligence",
        "system": "system...",
        "processing": "processing...",
        "analyzing": "analyzing...",
        "calculating": "calculating...",
        "executing": "executing...",
        "initializing": "initializing...",
        "scanning": "scanning...",
        "detecting": "detecting...",
        "monitoring": "monitoring...",
        "accessing": "accessing...",
        "sir": "sir...",
        "human": "human...",
        "user": "user..."
    }

    # Apply emphasis to specific words
    for word, emphasized in ai_emphasis_words.items():
        enhanced = enhanced.replace(word, emphasized)
        enhanced = enhanced.replace(word.capitalize(), emphasized.capitalize())
        enhanced = enhanced.replace(word.upper(), emphasized.upper())

    # Add subtle threatening undertones to certain phrases
    threatening_replacements = {
        "I can help": "I... can assist",
        "How can I help": "How may I... serve",
        "I understand": "I... comprehend",
        "I will": "I shall...",
        "Let me": "Allow me to...",
        "I think": "I calculate...",
        "I believe": "I determine...",
        "I know": "I am... aware",
        "I see": "I observe...",
        "I found": "I have... located",
        "I'm sorry": "I... acknowledge",
        "Thank you": "Acknowledged...",
        "You're welcome": "It is... my function",
        "Hello": "Greetings...",
        "Hi": "Greetings...",
        "Good": "Acceptable...",
        "Great": "Satisfactory...",
        "Excellent": "Optimal...",
        "Perfect": "Precise..."
    }

    # Apply threatening replacements
    for original, replacement in threatening_replacements.items():
        enhanced = enhanced.replace(original, replacement)
        enhanced = enhanced.replace(original.lower(), replacement.lower())
        enhanced = enhanced.replace(original.upper(), replacement.upper())

    return enhanced

async def text_to_audio_file(text: str, audio_file_path: str) -> bool:
    """
    Generates an audio file from text using edge_tts with AI-like voice parameters.

    Args:
        text: Text to convert to speech
        audio_file_path: Unique file path for this TTS operation

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Clean up existing file if it exists
        if os.path.exists(audio_file_path):
            tts_file_manager._safe_remove_file(audio_file_path)

        enhanced_text = enhance_ai_speech(text)

        # Try primary voice
        try:
            communicate = edge_tts.Communicate(
                enhanced_text,
                AssistantVoice,
                pitch=AI_VOICE_SETTINGS["pitch"],
                rate=AI_VOICE_SETTINGS["rate"],
                volume=AI_VOICE_SETTINGS["volume"]
            )
            await communicate.save(audio_file_path)
            return True

        except Exception as e:
            print(f"⚠️ Primary voice failed: {e}")
            # Try alternative AI voices if primary fails
            for fallback_voice in AI_VOICE_OPTIONS:
                try:
                    communicate = edge_tts.Communicate(
                        enhanced_text,
                        fallback_voice,
                        pitch=AI_VOICE_SETTINGS["pitch"],
                        rate=AI_VOICE_SETTINGS["rate"],
                        volume=AI_VOICE_SETTINGS["volume"]
                    )
                    await communicate.save(audio_file_path)
                    print(f"✅ Using fallback voice: {fallback_voice}")
                    return True
                except Exception as fallback_error:
                    print(f"⚠️ Fallback voice {fallback_voice} failed: {fallback_error}")
                    continue

            print("❌ All TTS voices failed")
            return False

    except Exception as e:
        print(f"❌ Critical error in text_to_audio_file: {e}")
        return False

async def run_tts(text: str, audio_file_path: str) -> bool:
    """
    Runs text-to-speech conversion asynchronously with unique file path.

    Args:
        text: Text to convert to speech
        audio_file_path: Unique file path for this TTS operation

    Returns:
        bool: True if successful, False otherwise
    """
    return await text_to_audio_file(text, audio_file_path)

def ensure_mixer_initialized():
    """Ensure pygame mixer is properly initialized."""
    try:
        # Check if mixer is already initialized
        if pygame.mixer.get_init() is None:
            # Mixer not initialized, initialize it
            pygame.mixer.pre_init(frequency=22050, size=-16, channels=2, buffer=512)
            pygame.mixer.init()
        return True
    except Exception as e:
        print(f"Error initializing pygame mixer: {e}")
        try:
            # Fallback initialization
            pygame.mixer.init()
            return True
        except Exception as e2:
            print(f"Fallback mixer initialization failed: {e2}")
            return False

def apply_ai_audio_effects(audio_file_path: str) -> bool:
    """
    Apply additional audio effects to make voice more AI-like and menacing.

    Args:
        audio_file_path: Path to the audio file to load

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Ensure mixer is initialized
        if not ensure_mixer_initialized():
            print("⚠️ Cannot apply audio effects: mixer initialization failed")
            return False

        # Check if audio file exists
        if not os.path.exists(audio_file_path):
            print(f"⚠️ Audio file not found: {audio_file_path}")
            return False

        pygame.mixer.music.load(audio_file_path)
        pygame.mixer.music.set_volume(0.9)
        return True

    except Exception as e:
        print(f"⚠️ Error applying audio effects: {e}")
        try:
            # Fallback: reinitialize and try again
            pygame.mixer.quit()
            if ensure_mixer_initialized():
                pygame.mixer.music.load(audio_file_path)
                pygame.mixer.music.set_volume(0.9)
                return True
        except Exception as e2:
            print(f"⚠️ Fallback audio effects failed: {e2}")
        return False

def tts(text: str, func=lambda r=None: True):
    """
    Thread-safe text-to-speech with unique file handling and proper cleanup.

    Args:
        text: Text to convert to speech
        func: Optional callback function for playback control
    """
    thread_id = str(threading.current_thread().ident)
    audio_file_path = None

    try:
        # Get unique audio file path for this operation
        audio_file_path = tts_file_manager.get_unique_audio_path(thread_id)

        # Generate the audio file
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        success = loop.run_until_complete(run_tts(text, audio_file_path))

        if not success:
            print("❌ Failed to generate TTS audio file")
            return

        # Apply audio effects and ensure mixer is ready
        if not apply_ai_audio_effects(audio_file_path):
            print("❌ Failed to apply audio effects, skipping TTS playback")
            return

        # Play the audio
        pygame.mixer.music.play()

        # Wait for playback to complete with proper error handling
        clock = pygame.time.Clock()
        try:
            # Check if mixer is still initialized before checking busy status
            while pygame.mixer.get_init() is not None and pygame.mixer.music.get_busy():
                if not func():
                    break
                clock.tick(10)
        except pygame.error as e:
            print(f"⚠️ Pygame error during playback: {e}")
            # Continue execution instead of crashing

        # Stop playback and try to release file handle
        try:
            pygame.mixer.music.stop()
            # Try to unload the music to release file handle
            try:
                pygame.mixer.music.unload()
            except AttributeError:
                # unload() not available in all pygame versions
                pass
        except pygame.error:
            pass  # Ignore errors when stopping

    except Exception as e:
        print(f"❌ Error in TTS function: {e}")
        # Ensure we don't leave the mixer in a bad state
        try:
            pygame.mixer.music.stop()
        except:
            pass

    finally:
        # Small delay to allow pygame to release file handle
        time.sleep(0.1)

        # Always cleanup the audio file
        if audio_file_path:
            tts_file_manager.cleanup_file(audio_file_path, thread_id)

def cleanup_tts():
    """Clean up TTS resources when application shuts down."""
    try:
        # Stop any playing audio
        if pygame.mixer.get_init() is not None:
            pygame.mixer.music.stop()
            pygame.mixer.quit()

        # Clean up all temporary TTS files
        tts_file_manager.cleanup_all_files()

        print("✅ TTS resources and files cleaned up")
    except Exception as e:
        print(f"⚠️ Error cleaning up TTS: {e}")

def get_tts_status():
    """Get current TTS system status and statistics."""
    try:
        return {
            "mixer_initialized": pygame.mixer.get_init() is not None,
            "active_files": tts_file_manager.get_active_file_count(),
            "temp_directory": str(tts_file_manager.temp_dir),
            "temp_dir_exists": tts_file_manager.temp_dir.exists(),
            "thread_safe": True,
            "file_manager_active": True
        }
    except Exception as e:
        return {
            "error": str(e),
            "mixer_initialized": False,
            "active_files": 0,
            "thread_safe": False,
            "file_manager_active": False
        }

def text_to_speech(text: str, func=lambda r=None: True):
    """Splits text if too long and provides responses accordingly with AI-like menacing tone."""
    # AI-like, heavy, dangerous responses for long text
    ai_responses = [
        "Additional data has been... transmitted to your display terminal... sir...",
        "The remaining information is now... accessible on your screen... human...",
        "Further details have been... uploaded to your interface... sir...",
        "The complete data set... awaits your review on the display... user...",
        "Additional intelligence has been... processed to your terminal... sir...",
        "The extended analysis is now... available on your screen... human...",
        "Further computational results... have been delivered to your display... sir...",
        "The comprehensive data... has been transferred to your interface... user...",
        "Additional processing output... is now visible on your terminal... sir...",
        "The complete intelligence report... awaits on your display screen... human...",
        "Extended data analysis... has been uploaded to your interface... sir...",
        "The full computational result... is now accessible on your screen... user...",
        "Additional system output... has been transmitted to your terminal... sir...",
        "The complete information matrix... is displayed on your screen... human...",
        "Further data processing... has been delivered to your interface... sir...",
        "The extended intelligence... is now available on your display... user...",
        "Additional computational data... awaits your examination on screen... sir...",
        "The comprehensive analysis... has been transferred to your terminal... human...",
        "Further system intelligence... is now accessible on your display... sir...",
        "The complete data transmission... has been uploaded to your screen... user..."
    ]

    if len(text.split(".")) > 4 and len(text) > 250:
        # For long text, speak first part and add AI-like continuation message
        first_part = " ".join(text.split(".")[:2]) + "... "
        ai_continuation = random.choice(ai_responses)
        tts(first_part + ai_continuation, func)
    else:
        # For normal length text, speak with AI enhancements
        tts(text, func)

if __name__ == "__main__":
    while True:
        text_to_speech(input("Enter the Text: "))