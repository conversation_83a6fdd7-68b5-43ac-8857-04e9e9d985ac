import os
import numpy as np
import pandas as pd
import librosa
import tensorflow as tf
from transformers import Wav2Vec2Processor, Wav2Vec2Model
import torch
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import json
import warnings

warnings.filterwarnings('ignore')

class EmotionModelTrainer:
    """
    Train a deep learning model for speech emotion recognition using Wav2Vec2 features.
    """
    
    def __init__(self, dataset_path: str = "Data/emotion_dataset"):
        self.dataset_path = dataset_path
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Emotion labels (7-class system)
        self.emotion_labels = [
            'anger', 'disgust', 'fear', 'happiness', 
            'pleasant_surprise', 'sadness', 'neutral'
        ]
        
        # Audio parameters
        self.sample_rate = 16000
        self.max_length = 3.0  # seconds
        
        # Model components
        self.wav2vec_processor = None
        self.wav2vec_model = None
        self.label_encoder = LabelEncoder()
        
        # Training data
        self.features = []
        self.labels = []
        self.file_paths = []
        
        print(f"🧠 Emotion Model Trainer initialized on: {self.device}")
    
    def load_wav2vec_model(self):
        """Load pre-trained Wav2Vec2 model for feature extraction."""
        try:
            print("🔄 Loading Wav2Vec2 model...")
            self.wav2vec_processor = Wav2Vec2Processor.from_pretrained(
                "facebook/wav2vec2-base-960h"
            )
            self.wav2vec_model = Wav2Vec2Model.from_pretrained(
                "facebook/wav2vec2-base-960h"
            ).to(self.device)
            self.wav2vec_model.eval()
            print("✅ Wav2Vec2 model loaded successfully")
        except Exception as e:
            print(f"❌ Error loading Wav2Vec2 model: {e}")
            raise
    
    def extract_features_from_audio(self, audio_path: str) -> np.ndarray:
        """Extract Wav2Vec2 features from audio file."""
        try:
            # Load audio
            audio, sr = librosa.load(audio_path, sr=self.sample_rate)
            
            # Ensure audio is the right length
            if len(audio) > self.sample_rate * self.max_length:
                audio = audio[:int(self.sample_rate * self.max_length)]
            elif len(audio) < self.sample_rate:
                # Pad short audio
                padding = self.sample_rate - len(audio)
                audio = np.pad(audio, (0, padding), mode='constant')
            
            # Process with Wav2Vec2
            inputs = self.wav2vec_processor(
                audio, 
                sampling_rate=self.sample_rate, 
                return_tensors="pt",
                padding=True
            ).to(self.device)
            
            with torch.no_grad():
                outputs = self.wav2vec_model(**inputs)
                # Use mean pooling over sequence dimension
                features = outputs.last_hidden_state.mean(dim=1).cpu().numpy()
            
            return features.flatten()
            
        except Exception as e:
            print(f"❌ Error extracting features from {audio_path}: {e}")
            return np.zeros(768)  # Return zero features as fallback
    
    def load_dataset(self):
        """Load emotion dataset from directory structure."""
        print("📂 Loading emotion dataset...")
        
        if not os.path.exists(self.dataset_path):
            print(f"❌ Dataset path not found: {self.dataset_path}")
            print("📝 Expected structure: /actor/emotion/word_files.wav")
            return False
        
        # Scan dataset directory
        for actor_dir in os.listdir(self.dataset_path):
            actor_path = os.path.join(self.dataset_path, actor_dir)
            if not os.path.isdir(actor_path):
                continue
                
            print(f"🎭 Processing actor: {actor_dir}")
            
            for emotion_dir in os.listdir(actor_path):
                emotion_path = os.path.join(actor_path, emotion_dir)
                if not os.path.isdir(emotion_path):
                    continue
                
                if emotion_dir not in self.emotion_labels:
                    print(f"⚠️ Unknown emotion: {emotion_dir}, skipping...")
                    continue
                
                # Process audio files in emotion directory
                audio_files = [f for f in os.listdir(emotion_path) if f.endswith('.wav')]
                print(f"  📁 {emotion_dir}: {len(audio_files)} files")
                
                for audio_file in tqdm(audio_files, desc=f"  Processing {emotion_dir}"):
                    audio_path = os.path.join(emotion_path, audio_file)
                    
                    # Extract features
                    features = self.extract_features_from_audio(audio_path)
                    
                    if features is not None and len(features) > 0:
                        self.features.append(features)
                        self.labels.append(emotion_dir)
                        self.file_paths.append(audio_path)
        
        print(f"✅ Dataset loaded: {len(self.features)} samples, {len(set(self.labels))} emotions")
        return len(self.features) > 0
    
    def prepare_data(self):
        """Prepare data for training."""
        if len(self.features) == 0:
            print("❌ No features loaded. Run load_dataset() first.")
            return False
        
        print("🔄 Preparing training data...")
        
        # Convert to numpy arrays
        X = np.array(self.features)
        y = self.label_encoder.fit_transform(self.labels)
        
        # Convert labels to categorical
        y_categorical = tf.keras.utils.to_categorical(y, num_classes=len(self.emotion_labels))
        
        # Split data
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X, y_categorical, test_size=0.2, random_state=42, stratify=y
        )
        
        print(f"✅ Data prepared:")
        print(f"  Training samples: {len(self.X_train)}")
        print(f"  Test samples: {len(self.X_test)}")
        print(f"  Feature dimension: {X.shape[1]}")
        
        return True
    
    def build_model(self):
        """Build the emotion classification model."""
        print("🏗️ Building emotion classification model...")
        
        self.model = tf.keras.Sequential([
            tf.keras.layers.Dense(512, activation='relu', input_shape=(768,)),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.BatchNormalization(),
            
            tf.keras.layers.Dense(256, activation='relu'),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.BatchNormalization(),
            
            tf.keras.layers.Dense(128, activation='relu'),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.BatchNormalization(),
            
            tf.keras.layers.Dense(len(self.emotion_labels), activation='softmax')
        ])
        
        # Compile model
        self.model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        print("✅ Model built successfully")
        print(self.model.summary())
    
    def train_model(self, epochs: int = 50, batch_size: int = 32):
        """Train the emotion classification model."""
        print(f"🚀 Training model for {epochs} epochs...")
        
        # Callbacks
        callbacks = [
            tf.keras.callbacks.EarlyStopping(
                monitor='val_loss', patience=10, restore_best_weights=True
            ),
            tf.keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss', factor=0.5, patience=5, min_lr=1e-6
            ),
            tf.keras.callbacks.ModelCheckpoint(
                'Data/emotion_model_best.h5', 
                monitor='val_accuracy', 
                save_best_only=True
            )
        ]
        
        # Train model
        history = self.model.fit(
            self.X_train, self.y_train,
            validation_data=(self.X_test, self.y_test),
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks,
            verbose=1
        )
        
        print("✅ Training completed!")
        return history
    
    def evaluate_model(self):
        """Evaluate the trained model."""
        print("📊 Evaluating model...")
        
        # Predictions
        y_pred = self.model.predict(self.X_test)
        y_pred_classes = np.argmax(y_pred, axis=1)
        y_true_classes = np.argmax(self.y_test, axis=1)
        
        # Classification report
        print("\n📈 Classification Report:")
        print(classification_report(
            y_true_classes, y_pred_classes, 
            target_names=self.emotion_labels
        ))
        
        # Confusion matrix
        cm = confusion_matrix(y_true_classes, y_pred_classes)
        
        plt.figure(figsize=(10, 8))
        sns.heatmap(
            cm, annot=True, fmt='d', 
            xticklabels=self.emotion_labels,
            yticklabels=self.emotion_labels,
            cmap='Blues'
        )
        plt.title('Emotion Classification Confusion Matrix')
        plt.ylabel('True Label')
        plt.xlabel('Predicted Label')
        plt.tight_layout()
        plt.savefig('Data/emotion_confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # Save evaluation results
        accuracy = np.mean(y_pred_classes == y_true_classes)
        results = {
            'accuracy': float(accuracy),
            'emotion_labels': self.emotion_labels,
            'confusion_matrix': cm.tolist(),
            'classification_report': classification_report(
                y_true_classes, y_pred_classes, 
                target_names=self.emotion_labels,
                output_dict=True
            )
        }
        
        with open('Data/emotion_model_evaluation.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"✅ Model accuracy: {accuracy:.4f}")
        return results
    
    def save_model(self):
        """Save the trained model and components."""
        print("💾 Saving model...")
        
        # Save model weights
        self.model.save_weights('Data/emotion_classifier_weights.h5')
        
        # Save label encoder
        import joblib
        joblib.dump(self.label_encoder, 'Data/emotion_label_encoder.pkl')
        
        # Save model configuration
        config = {
            'emotion_labels': self.emotion_labels,
            'sample_rate': self.sample_rate,
            'max_length': self.max_length,
            'feature_dim': 768,
            'model_architecture': 'wav2vec2_classifier'
        }
        
        with open('Data/emotion_model_config.json', 'w') as f:
            json.dump(config, f, indent=2)
        
        print("✅ Model saved successfully!")

def main():
    """Main training pipeline."""
    print("🎯 Starting Emotion Model Training Pipeline...")
    
    # Initialize trainer
    trainer = EmotionModelTrainer()
    
    # Load Wav2Vec2 model
    trainer.load_wav2vec_model()
    
    # Load dataset
    if not trainer.load_dataset():
        print("❌ Failed to load dataset")
        return
    
    # Prepare data
    if not trainer.prepare_data():
        print("❌ Failed to prepare data")
        return
    
    # Build model
    trainer.build_model()
    
    # Train model
    history = trainer.train_model(epochs=50, batch_size=32)
    
    # Evaluate model
    results = trainer.evaluate_model()
    
    # Save model
    trainer.save_model()
    
    print("🎉 Training pipeline completed successfully!")

if __name__ == "__main__":
    main()
