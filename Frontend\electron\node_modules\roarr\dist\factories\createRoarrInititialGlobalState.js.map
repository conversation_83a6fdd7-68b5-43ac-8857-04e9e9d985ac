{"version": 3, "sources": ["../../src/factories/createRoarrInititialGlobalState.js"], "names": ["currentState", "versions", "concat", "sort", "cmp", "currentIsLatestVersion", "length", "version", "includes", "push", "newState", "sequence", "environmentIsNode", "write"], "mappings": ";;;;;;;AAEA;;AACA;;AACA;;AAMA;;;;;;;;;;AAEA;wCACgBA,Y,IAA+C;AAC7D,QAAMC,QAAQ,GAAG,CAACD,YAAY,CAACC,QAAb,IAAyB,EAA1B,EAA8BC,MAA9B,EAAjB;AAEAD,EAAAA,QAAQ,CAACE,IAAT,CAAcC,sBAAd;AAEA,QAAMC,sBAAsB,GAAG,CAACJ,QAAQ,CAACK,MAAV,IAAoB,4BAAIC,gBAAJ,EAAaN,QAAQ,CAACA,QAAQ,CAACK,MAAT,GAAkB,CAAnB,CAArB,MAAgD,CAAnG;;AAEA,MAAI,CAACL,QAAQ,CAACO,QAAT,CAAkBD,gBAAlB,CAAL,EAAiC;AAC/BN,IAAAA,QAAQ,CAACQ,IAAT,CAAcF,gBAAd;AACD;;AAEDN,EAAAA,QAAQ,CAACE,IAAT,CAAcC,sBAAd;;AAEA,MAAIM,QAAQ;AACVC,IAAAA,QAAQ,EAAE;AADA,KAEPX,YAFO;AAGVC,IAAAA;AAHU,IAAZ;;AAMA,MAAIW,mBAAJ,EAAuB;AACrB,QAAIP,sBAAsB,IAAI,CAACK,QAAQ,CAACG,KAAxC,EAA+C;AAC7CH,MAAAA,QAAQ,mCACHA,QADG,GAEH,gCAFG,CAAR;AAID;AACF;;AAED,SAAOA,QAAP;AACD,C", "sourcesContent": ["// @flow\n\nimport environmentIsNode from 'detect-node';\nimport cmp from 'semver-compare';\nimport {\n  version,\n} from '../../package.json';\nimport type {\n  RoarrGlobalStateType,\n} from '../types';\nimport createNodeWriter from './createNodeWriter';\n\n// eslint-disable-next-line flowtype/no-weak-types\nexport default (currentState: Object): RoarrGlobalStateType => {\n  const versions = (currentState.versions || []).concat();\n\n  versions.sort(cmp);\n\n  const currentIsLatestVersion = !versions.length || cmp(version, versions[versions.length - 1]) === 1;\n\n  if (!versions.includes(version)) {\n    versions.push(version);\n  }\n\n  versions.sort(cmp);\n\n  let newState = {\n    sequence: 0,\n    ...currentState,\n    versions,\n  };\n\n  if (environmentIsNode) {\n    if (currentIsLatestVersion || !newState.write) {\n      newState = {\n        ...newState,\n        ...createNodeWriter(),\n      };\n    }\n  }\n\n  return newState;\n};\n"], "file": "createRoarrInititialGlobalState.js"}