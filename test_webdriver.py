#!/usr/bin/env python3
"""
Test script to diagnose WebDriver initialization issues
"""

import time
import sys
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriver<PERSON>anager

def test_webdriver_initialization():
    """Test WebDriver initialization with detailed logging"""
    print("🧪 Testing WebDriver initialization...")
    
    # Configure Chrome options
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--allow-running-insecure-content")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-plugins")
    chrome_options.add_argument("--disable-images")
    chrome_options.add_argument("--disable-javascript")
    chrome_options.add_argument("--mute-audio")
    chrome_options.add_argument("--no-first-run")
    chrome_options.add_argument("--no-default-browser-check")
    chrome_options.add_argument("--disable-default-apps")
    chrome_options.add_argument("--disable-popup-blocking")
    chrome_options.add_argument("--disable-translate")
    chrome_options.add_argument("--disable-background-timer-throttling")
    chrome_options.add_argument("--disable-renderer-backgrounding")
    chrome_options.add_argument("--disable-backgrounding-occluded-windows")
    chrome_options.add_argument("--disable-ipc-flooding-protection")
    chrome_options.add_argument("--window-size=1,1")
    chrome_options.add_argument("--window-position=-2000,-2000")
    
    user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/538.36"
    chrome_options.add_argument(f'user-agent={user_agent}')
    
    try:
        print("📥 Step 1: Installing ChromeDriver...")
        start_time = time.time()
        
        # This is where it might be hanging
        service = Service(ChromeDriverManager().install())
        install_time = time.time() - start_time
        print(f"✅ ChromeDriver installed in {install_time:.2f} seconds")
        
        print("🚀 Step 2: Starting Chrome WebDriver...")
        start_time = time.time()
        
        driver = webdriver.Chrome(service=service, options=chrome_options)
        init_time = time.time() - start_time
        print(f"✅ Chrome WebDriver started in {init_time:.2f} seconds")
        
        print("🌐 Step 3: Testing navigation...")
        start_time = time.time()
        
        driver.get("https://www.google.com")
        nav_time = time.time() - start_time
        print(f"✅ Navigation completed in {nav_time:.2f} seconds")
        
        print("🧹 Step 4: Cleaning up...")
        driver.quit()
        print("✅ WebDriver test completed successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ WebDriver test failed: {e}")
        print(f"❌ Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 Matrix AI WebDriver Diagnostic Test")
    print("=" * 50)
    
    success = test_webdriver_initialization()
    
    if success:
        print("\n✅ WebDriver is working correctly!")
        sys.exit(0)
    else:
        print("\n❌ WebDriver has issues that need to be resolved!")
        sys.exit(1)
