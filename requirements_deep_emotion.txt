# Deep Learning Speech Emotion Recognition Dependencies
# Install with: pip install -r requirements_deep_emotion.txt

# Core Deep Learning Frameworks
tensorflow>=2.12.0
torch>=2.0.0
transformers>=4.21.0

# Audio Processing
librosa>=0.10.0
soundfile>=0.12.0

# Data Science and Visualization
pandas>=1.5.0
matplotlib>=3.6.0
numpy>=1.21.0
scikit-learn>=1.1.0

# Additional Audio Processing
scipy>=1.9.0
resampy>=0.4.0

# Model Optimization (Optional)
onnx>=1.12.0
onnxruntime>=1.12.0

# Progress Bars and Utilities
tqdm>=4.64.0
joblib>=1.2.0

# For CUDA support (if available)
# torch-audio>=2.0.0  # Uncomment if using GPU

# Note: Install PyTorch with CUDA support separately if needed:
# pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
