# BACKUP: This file has been backed up to Frontend/GUI_PyQt5_Backup.py
# The GUI has been converted to use Electron with HTML/CSS/JS

import sys
import os
import subprocess
import time

# Add parent directory to path to import Data.DLLs
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from Data.DLLs import *

# Global variables
old_chat_message = ""

def AnswerModifier(Answer):
    lines = Answer.split('\n')
    non_empty_lines = [line for line in lines if line.strip()]
    return '\n'.join(non_empty_lines)

def QueryModifier(Query):
    new_query = Query.lower().strip()
    query_words = new_query.split()
    question_words = ["how", "what", "who", "where", "when", "why", "which", "whose", "whom", "can you", "what is", "where's", "how's"]
    if any(word + " " in new_query for word in question_words):
        if query_words[-1][-1] in ['.', '?', '!']:
            new_query = new_query[:-1]
        else:
            new_query += "?"
    else:
        if query_words[-1][-1] in ['.', '?', '!']:
            new_query = new_query[:-1] + "."
        else:
            new_query += "."
    return new_query.capitalize()

def SetMicrophoneStatus(command):
    try:
        with open(os.path.join(TempDirectoryPath, "Mic.data"), "w", encoding='utf-8') as file:
            file.write(command)
    except Exception as e:
        print("Error writing Mic.data:", e)

def GetMicrophoneStatus():
    try:
        with open(os.path.join(TempDirectoryPath, "Mic.data"), "r", encoding='utf-8') as file:
            return file.read().strip()
    except Exception as e:
        print("Error reading Mic.data:", e)
        return ""

def SetAssistantStatus(Status):
    file_path = os.path.join(TempDirectoryPath, "Status.data")
    if not os.path.exists(TempDirectoryPath):
        os.makedirs(TempDirectoryPath)
    try:
        with open(file_path, "w", encoding="utf-8") as file:
            file.write(Status)
    except Exception as e:
        print("Error writing Status.data:", e)

def GetAssistantStatus():
    file_path = os.path.join(TempDirectoryPath, "Status.data")
    if not os.path.exists(TempDirectoryPath):
        os.makedirs(TempDirectoryPath)
    if not os.path.exists(file_path):
        SetAssistantStatus("")
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            return file.read().strip()
    except Exception as e:
        print("Error reading Status.data:", e)
        return ""

def MicButtonInitialed():
    SetMicrophoneStatus("True")

def MicButtonClosed():
    SetMicrophoneStatus("False")

def GraphicsPath(Filename):
    return os.path.join(GraphicsDirectoryPath, Filename)

def TempPath(Filename):
    return os.path.join(TempDirectoryPath, Filename)

def ShowTextToScreen(Text):
    try:
        with open(os.path.join(TempDirectoryPath, "Responses.data"), "w", encoding='utf-8') as file:
            file.write(Text)
    except Exception as e:
        print("Error writing Responses.data:", e)

# --- Electron GUI Implementation ---

class ElectronGUI:
    def __init__(self):
        self.electron_process = None
        self.gui_ready = False

    def create_html_file(self):
        """Create the HTML file for the Electron GUI"""
        html_content = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Friday_UI</title>
    <style>
        body,
html {
  margin: 0;
  padding: 0;
  background-color: #111;
  font-family: 'Consolas', 'Courier New', monospace;
}

.container,
canvas {
  max-width: 100%;
  width: 100%;
}

#centeredText {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 4rem;
  font-weight: bold;
  color: #00FF00;
  text-shadow: 0 0 20px rgba(0, 255, 0, 0.8);
  font-family: 'Consolas', monospace;
  z-index: 1000;
  pointer-events: none;
}

/* Microphone Button */
#micButton {
  position: fixed;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: rgba(0, 255, 0, 0.2);
  border: 3px solid #00FF00;
  color: #00FF00;
  font-size: 32px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 1000;
}

#micButton:hover {
  background-color: rgba(0, 255, 0, 0.4);
  border: 3px solid #00FFFF;
  transform: translateX(-50%) scale(1.1);
}

#micButton.active {
  background-color: rgba(0, 255, 0, 0.6);
  border: 3px solid #00FFFF;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

/* Status Display - Bottom Left */
#statusDisplay {
  position: fixed;
  bottom: 20px;
  left: 20px;
  padding: 10px 20px;
  background-color: rgba(0, 0, 0, 0.7);
  border: 1px solid #00AA00;
  border-radius: 5px;
  color: #00FF00;
  font-family: 'Consolas', monospace;
  font-size: 14px;
  text-align: center;
  min-width: 200px;
}

/* Emotion Display - Bottom Right */
#emotionDisplay {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 10px 20px;
  background-color: rgba(0, 0, 0, 0.7);
  border: 1px solid #00AA00;
  border-radius: 5px;
  color: #00FF00;
  font-family: 'Consolas', monospace;
  font-size: 14px;
  text-align: center;
  min-width: 200px;
}
    </style>
</head>
<body>
    <!-- Main Interface -->
    <div class="container">
      <canvas id="waveCanvas"></canvas>
    </div>

    <!-- Centered Text -->
    <div id="centeredText">Matrix</div>

    <!-- Status Display -->
    <div id="statusDisplay">Initializing...</div>

    <!-- Emotion Display -->
    <div id="emotionDisplay">Neutral</div>

    <!-- Microphone Button -->
    <div id="micButton" onclick="toggleMicrophone()">🎤</div>

<script>
    // Global variables
    let microphoneActive = false;

    // Canvas and animation setup
    const canvas = document.getElementById("waveCanvas");
    const ctx = canvas.getContext("2d");
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    const turbulenceFactor = 0.25;
    const maxAmplitude = canvas.height / 3.5;
    const baseLine = canvas.height / 2;
    const numberOfWaves = 10;
    let globalTime = 0;

    function createGradient() {
      const gradient = ctx.createLinearGradient(0, 0, canvas.width, 0);
      gradient.addColorStop(0, "rgba(255, 25, 255, 0.2)");
      gradient.addColorStop(0.5, "rgba(25, 255, 255, 0.75)");
      gradient.addColorStop(1, "rgba(255, 255, 25, 0.2)");
      return gradient;
    }

    const gradient = createGradient();

    function generateSmoothWave(dataArray, frequency = 0.1, amplitude = 64) {
      const array = new Uint8Array(100);
      for (let i = 0; i < array.length; i++) {
        array[i] = (Math.sin(i * frequency + globalTime) + 1) * amplitude;
      }

      return array;
    }

    function animateWaves(dataArray, analyser) {
      const isSpeaking = dataArray.some((value) => value > 0);
      if (isSpeaking) {
        // Speaking
        analyser.getByteFrequencyData(dataArray);
      } else {
        // Thinking Mode
        dataArray = generateSmoothWave(dataArray, 0.05, 16);
      }
      drawWave(dataArray, analyser);
    }

    navigator.mediaDevices
      .getUserMedia({ audio: true, video: false })
      .then((stream) => {
        const audioContext = new (window.AudioContext ||
          window.webkitAudioContext)();
        const analyser = audioContext.createAnalyser();
        const microphone = audioContext.createMediaStreamSource(stream);
        microphone.connect(analyser);
        const dataArray = new Uint8Array(analyser.frequencyBinCount);
        const waves = dataArray.slice(0, 250);
        animateWaves(waves, analyser);
      })
      .catch((error) => {
        console.error("Access to microphone denied", error);
      });

    function drawWave(dataArray, analyser) {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      globalTime += 0.05;

      for (let j = 0; j < numberOfWaves; j++) {
        ctx.beginPath();
        ctx.lineWidth = 2;
        ctx.strokeStyle = gradient;

        let x = 0;
        let sliceWidth = (canvas.width * 1.0) / dataArray.length;
        let lastX = 0;
        let lastY = baseLine;

        for (let i = 0; i < dataArray.length; i++) {
          const v = dataArray[i] / 96.0;
          const mid = dataArray.length / 2;
          const distanceFromMid = Math.abs(i - mid) / mid;
          const dampFactor = 1 - Math.pow((2 * i) / dataArray.length - 1, 2);

          const amplitude = maxAmplitude * dampFactor * (1 - distanceFromMid);
          const isWaveInverted = j % 2 ? 1 : -1;
          const frequency = isWaveInverted * (0.05 + turbulenceFactor);

          const y =
            baseLine + Math.sin(i * frequency + globalTime + j) * amplitude * v;

          if (i === 0) {
            ctx.moveTo(x, y);
          } else {
            let xc = (x + lastX) / 2;
            let yc = (y + lastY) / 2;
            ctx.quadraticCurveTo(lastX, lastY, xc, yc);
          }

          lastX = x;
          lastY = y;
          x += sliceWidth;
        }

        ctx.lineTo(canvas.width, lastY);
        ctx.stroke();
      }

      requestAnimationFrame(() => animateWaves(dataArray, analyser));
    }



    // Microphone toggle function
    function toggleMicrophone() {
        microphoneActive = !microphoneActive;
        const micButton = document.getElementById('micButton');

        if (microphoneActive) {
            micButton.classList.add('active');
            micButton.innerHTML = '🎤';
            // Write to Mic.data file
            writeMicrophoneStatus('True');
            console.log('Microphone activated');
        } else {
            micButton.classList.remove('active');
            micButton.innerHTML = '🔇';
            // Write to Mic.data file
            writeMicrophoneStatus('False');
            console.log('Microphone deactivated');
        }
    }

    // File I/O functions (using Electron's file system access)
    function writeMicrophoneStatus(status) {
        // This will be handled by Electron's main process
        if (window.electronAPI) {
            window.electronAPI.writeMicStatus(status);
        }
    }



    function displayStatus() {
        // This will be handled by Electron's main process
        if (window.electronAPI) {
            window.electronAPI.readStatusFile().then(data => {
                const statusDiv = document.getElementById('statusDisplay');
                if (data && data.trim()) {
                    statusDiv.innerText = data;
                }
            }).catch(error => {
                console.error('Error fetching status:', error);
            });
        }
    }

    function displayEmotion() {
        // This will be handled by Electron's main process
        if (window.electronAPI) {
            window.electronAPI.readEmotionFile().then(data => {
                const emotionDiv = document.getElementById('emotionDisplay');
                if (data && data.trim()) {
                    emotionDiv.innerText = data;
                } else {
                    emotionDiv.innerText = 'Neutral';
                }
            }).catch(error => {
                console.error('Error fetching emotion:', error);
                const emotionDiv = document.getElementById('emotionDisplay');
                if (emotionDiv) {
                    emotionDiv.innerText = 'Neutral';
                }
            });
        }
    }



    // Initialize the interface
    window.addEventListener('load', function() {
        // Set initial microphone state to active
        microphoneActive = true;
        toggleMicrophone();

        // Start periodic updates
        setInterval(() => {
            displayStatus();
            displayEmotion();
        }, 1000);

        console.log('Matrix UI initialized');
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
    });
</script>
</body>
</html>"""

        return html_content

    def create_electron_main_js(self):
        """Create the main.js file for Electron"""
        main_js_content = """const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

let mainWindow;

function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'preload.js')
        },
        icon: path.join(__dirname, 'assets', 'icon.png'),
        title: 'Matrix AI Assistant - Friday UI',
        backgroundColor: '#000000',
        show: false
    });

    mainWindow.loadFile('friday_ui.html');

    // Show window when ready
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        console.log('Electron GUI ready');
    });

    mainWindow.on('closed', () => {
        mainWindow = null;
    });
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// IPC handlers for file operations
ipcMain.handle('write-mic-status', async (event, status) => {
    try {
        const filePath = path.join(__dirname, '..', 'Files', 'Mic.data');
        fs.writeFileSync(filePath, status, 'utf8');
        return { success: true };
    } catch (error) {
        console.error('Error writing mic status:', error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('read-response-file', async () => {
    try {
        const filePath = path.join(__dirname, '..', 'Files', 'Responses.data');
        if (fs.existsSync(filePath)) {
            return fs.readFileSync(filePath, 'utf8');
        }
        return '';
    } catch (error) {
        console.error('Error reading response file:', error);
        return '';
    }
});

ipcMain.handle('read-status-file', async () => {
    try {
        const filePath = path.join(__dirname, '..', 'Files', 'Status.data');
        if (fs.existsSync(filePath)) {
            return fs.readFileSync(filePath, 'utf8');
        }
        return 'Ready...';
    } catch (error) {
        console.error('Error reading status file:', error);
        return 'Ready...';
    }
});

ipcMain.handle('read-emotion-file', async () => {
    try {
        const filePath = path.join(__dirname, '..', 'Files', 'Emotion.data');
        if (fs.existsSync(filePath)) {
            return fs.readFileSync(filePath, 'utf8');
        }
        return 'Neutral';
    } catch (error) {
        console.error('Error reading emotion file:', error);
        return 'Neutral';
    }
});
"""
        return main_js_content

    def create_preload_js(self):
        """Create the preload.js file for Electron"""
        preload_js_content = """const { contextBridge, ipcRenderer } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
    writeMicStatus: (status) => ipcRenderer.invoke('write-mic-status', status),
    readResponseFile: () => ipcRenderer.invoke('read-response-file'),
    readStatusFile: () => ipcRenderer.invoke('read-status-file'),
    readEmotionFile: () => ipcRenderer.invoke('read-emotion-file')
});
"""
        return preload_js_content

    def create_package_json(self):
        """Create the package.json file for Electron"""
        package_json_content = """{
  "name": "matrix-ai-friday-ui",
  "version": "1.0.0",
  "description": "Matrix AI Assistant - Friday UI",
  "main": "main.js",
  "scripts": {
    "start": "electron .",
    "dev": "electron . --dev"
  },
  "keywords": ["electron", "ai", "assistant", "matrix"],
  "author": "Matrix AI",
  "license": "MIT",
  "devDependencies": {
    "electron": "^latest"
  }
}"""
        return package_json_content

    def setup_electron_files(self):
        """Create all necessary Electron files"""
        try:
            # Create Frontend/electron directory
            electron_dir = os.path.join("Frontend", "electron")
            os.makedirs(electron_dir, exist_ok=True)

            # Create HTML file
            html_path = os.path.join(electron_dir, "friday_ui.html")
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(self.create_html_file())

            # Create main.js file
            main_js_path = os.path.join(electron_dir, "main.js")
            with open(main_js_path, 'w', encoding='utf-8') as f:
                f.write(self.create_electron_main_js())

            # Create preload.js file
            preload_js_path = os.path.join(electron_dir, "preload.js")
            with open(preload_js_path, 'w', encoding='utf-8') as f:
                f.write(self.create_preload_js())

            # Create package.json file
            package_json_path = os.path.join(electron_dir, "package.json")
            with open(package_json_path, 'w', encoding='utf-8') as f:
                f.write(self.create_package_json())

            print("✅ Electron files created successfully!")
            return electron_dir

        except Exception as e:
            print(f"❌ Error creating Electron files: {e}")
            return None

    def start_electron_gui(self):
        """Start the Electron GUI"""
        try:
            electron_dir = self.setup_electron_files()
            if not electron_dir:
                return False

            # Check if npm/electron is available
            try:
                # Try to install electron if not present
                print("📦 Installing Electron dependencies...")
                install_result = subprocess.run(
                    ["npm", "install"],
                    cwd=electron_dir,
                    capture_output=True,
                    text=True,
                    timeout=120
                )

                if install_result.returncode != 0:
                    print(f"⚠️ npm install failed: {install_result.stderr}")
                    print("🔄 Trying with global electron...")

            except (subprocess.TimeoutExpired, FileNotFoundError) as e:
                print(f"⚠️ npm not found or timeout: {e}")
                print("🔄 Trying with global electron...")

            # Start Electron
            print("🚀 Starting Electron GUI...")

            # Try to find and use the local electron installation
            electron_exe = os.path.join(electron_dir, "node_modules", ".bin", "electron.cmd")
            if not os.path.exists(electron_exe):
                electron_exe = os.path.join(electron_dir, "node_modules", "electron", "dist", "electron.exe")

            if os.path.exists(electron_exe):
                try:
                    print(f"🔄 Using local Electron: {electron_exe}")
                    self.electron_process = subprocess.Popen(
                        [electron_exe, "."],
                        cwd=electron_dir,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE
                    )
                    time.sleep(2)
                    if self.electron_process.poll() is None:
                        print("✅ Electron started successfully with local installation")
                    else:
                        print("❌ Local Electron failed to start")
                        self.electron_process = None
                except Exception as e:
                    print(f"❌ Error starting local Electron: {e}")
                    self.electron_process = None

            # If local electron failed, try system commands
            if not self.electron_process:
                electron_commands = [
                    ["npm", "start"],
                    ["npx", "electron", "."],
                    ["electron", "."]
                ]

                for cmd in electron_commands:
                    try:
                        print(f"🔄 Trying system command: {' '.join(cmd)}")
                        self.electron_process = subprocess.Popen(
                            cmd,
                            cwd=electron_dir,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            shell=True  # Use shell for Windows compatibility
                        )
                        time.sleep(2)
                        if self.electron_process.poll() is None:
                            print(f"✅ Electron started successfully with: {' '.join(cmd)}")
                            break
                        else:
                            print(f"❌ Command failed: {' '.join(cmd)}")
                            self.electron_process = None
                    except Exception as e:
                        print(f"❌ Error with command {' '.join(cmd)}: {e}")
                        self.electron_process = None
                        continue

            if not self.electron_process:
                print("❌ All Electron startup methods failed")
                print("💡 You can manually run: cd Frontend/electron && npm start")
                return False

            self.gui_ready = True
            print("✅ Electron GUI started successfully!")
            return True

        except Exception as e:
            print(f"❌ Error starting Electron GUI: {e}")
            return False

    def stop_electron_gui(self):
        """Stop the Electron GUI"""
        if self.electron_process:
            try:
                self.electron_process.terminate()
                self.electron_process.wait(timeout=5)
                print("✅ Electron GUI stopped")
            except subprocess.TimeoutExpired:
                self.electron_process.kill()
                print("⚠️ Electron GUI force killed")
            except Exception as e:
                print(f"❌ Error stopping Electron GUI: {e}")
            finally:
                self.electron_process = None
                self.gui_ready = False

# --- Startup coordination functions ---
def signal_gui_ready():
    """Signal that GUI is ready"""
    try:
        with open(TempPath('GUI_Ready.data'), 'w') as f:
            f.write('ready')
        print("✅ GUI ready signal sent")
    except Exception as e:
        print(f"Error signaling GUI ready: {e}")

def is_loading_complete():
    """Check if loading screen is complete"""
    try:
        return os.path.exists(TempPath('LoadingComplete.data'))
    except Exception:
        return False

def check_and_show_window_after_loading():
    """Check if loading is complete and show the window"""
    if is_loading_complete():
        print("🎉 Loading screen complete! Showing main interface...")
        return True
    return False

# Global Electron GUI instance
electron_gui = None

def GraphicalUserInterface():
    """Main function to start the Electron-based GUI"""
    global electron_gui

    try:
        print("🚀 Starting Electron-based GUI...")

        # Signal that GUI is ready (but hidden during loading)
        signal_gui_ready()

        # Wait for loading screen to complete
        print("⏳ Waiting for loading screen to complete...")
        while not check_and_show_window_after_loading():
            time.sleep(0.5)

        # Create and start Electron GUI
        electron_gui = ElectronGUI()
        success = electron_gui.start_electron_gui()

        if success:
            print("✅ Electron GUI started successfully!")
            print("🖥️ The GUI is now running in a dedicated desktop window")
            print("🔄 The interface will automatically update with AI responses")

            # Keep the process alive to maintain the Electron window
            try:
                print("📡 Monitoring Electron process... (Press Ctrl+C to stop)")
                while electron_gui.gui_ready and electron_gui.electron_process:
                    if electron_gui.electron_process.poll() is not None:
                        print("📱 Electron process ended")
                        break
                    time.sleep(1)
            except KeyboardInterrupt:
                print("🛑 Keyboard interrupt received")
            finally:
                electron_gui.stop_electron_gui()
                return True
        else:
            print("❌ Failed to start Electron GUI")
            return False

    except Exception as e:
        print(f"❌ Error in GraphicalUserInterface: {e}")
        if electron_gui:
            electron_gui.stop_electron_gui()
        return False

if __name__ == "__main__":
    GraphicalUserInterface()