const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

let mainWindow;

function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'preload.js')
        },
        icon: path.join(__dirname, 'assets', 'icon.png'),
        title: 'Matrix AI Assistant - Friday UI',
        backgroundColor: '#000000',
        show: false
    });

    mainWindow.loadFile('friday_ui.html');

    // Show window when ready
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        console.log('Electron GUI ready');
    });

    mainWindow.on('closed', () => {
        mainWindow = null;
    });
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// IPC handlers for file operations
ipcMain.handle('write-mic-status', async (event, status) => {
    try {
        const filePath = path.join(__dirname, '..', 'Files', 'Mic.data');
        fs.writeFileSync(filePath, status, 'utf8');
        return { success: true };
    } catch (error) {
        console.error('Error writing mic status:', error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('read-response-file', async () => {
    try {
        const filePath = path.join(__dirname, '..', 'Files', 'Responses.data');
        if (fs.existsSync(filePath)) {
            return fs.readFileSync(filePath, 'utf8');
        }
        return '';
    } catch (error) {
        console.error('Error reading response file:', error);
        return '';
    }
});

ipcMain.handle('read-status-file', async () => {
    try {
        const filePath = path.join(__dirname, '..', 'Files', 'Status.data');
        if (fs.existsSync(filePath)) {
            return fs.readFileSync(filePath, 'utf8');
        }
        return 'Ready...';
    } catch (error) {
        console.error('Error reading status file:', error);
        return 'Ready...';
    }
});

ipcMain.handle('read-emotion-file', async () => {
    try {
        const filePath = path.join(__dirname, '..', 'Files', 'Emotion.data');
        if (fs.existsSync(filePath)) {
            return fs.readFileSync(filePath, 'utf8');
        }
        return 'Neutral';
    } catch (error) {
        console.error('Error reading emotion file:', error);
        return 'Neutral';
    }
});
