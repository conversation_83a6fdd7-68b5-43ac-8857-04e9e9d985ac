{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "speechemotion_ml_algorithms.ipynb", "provenance": [], "collapsed_sections": [], "toc_visible": true, "authorship_tag": "ABX9TyMOovI2U0ot8M87YGFf6HB8", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/blhprasanna99/speech_emotion_detection/blob/master/speechemotion_ml_algorithms.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "code", "metadata": {"id": "uWhIg4du-1wV", "colab_type": "code", "outputId": "339308c7-d31f-4713-fa26-6ae1beddc3ab", "colab": {"base_uri": "https://localhost:8080/", "height": 54}}, "source": ["from google.colab import drive\n", "drive.mount('/content/drive')"], "execution_count": 0, "outputs": [{"output_type": "stream", "text": ["Drive already mounted at /content/drive; to attempt to forcibly remount, call drive.mount(\"/content/drive\", force_remount=True).\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "jrWB7JZg_VmF", "colab_type": "code", "outputId": "7d8250e9-e5ce-4002-b5c4-91886802b6ae", "colab": {"base_uri": "https://localhost:8080/", "height": 139}}, "source": ["!pip install soundfile"], "execution_count": 0, "outputs": [{"output_type": "stream", "text": ["Collecting soundfile\n", "  Downloading https://files.pythonhosted.org/packages/eb/f2/3cbbbf3b96fb9fa91582c438b574cff3f45b29c772f94c400e2c99ef5db9/SoundFile-0.10.3.post1-py2.py3-none-any.whl\n", "Requirement already satisfied: cffi>=1.0 in /usr/local/lib/python3.6/dist-packages (from soundfile) (1.13.2)\n", "Requirement already satisfied: pycparser in /usr/local/lib/python3.6/dist-packages (from cffi>=1.0->soundfile) (2.19)\n", "Installing collected packages: soundfile\n", "Successfully installed soundfile-0.10.3.post1\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "2LCe-eWs_P-c", "colab_type": "code", "colab": {}}, "source": ["import soundfile\n", "import numpy as np\n", "import librosa\n", "import glob\n", "import os\n", "from sklearn.model_selection import train_test_split\n", "\n", "# all emotions on RAVDESS dataset\n", "int2emotion = {\n", "    \"01\": \"neutral\",\n", "    \"02\": \"calm\",\n", "    \"03\": \"happy\",\n", "    \"04\": \"sad\",\n", "    \"05\": \"angry\",\n", "    \"06\": \"fearful\",\n", "    \"07\": \"disgust\",\n", "    \"08\": \"surprised\"\n", "}\n", "\n", "# we allow only these emotions\n", "AVAILABLE_EMOTIONS = {\n", "    \"angry\",\n", "    \"sad\",\n", "    \"neutral\",\n", "    \"happy\"\n", "}"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "wQlrNgst_oa0", "colab_type": "code", "colab": {}}, "source": ["def extract_feature(file_name, **kwargs):\n", "    \"\"\"\n", "    Extract feature from audio file `file_name`\n", "        Features supported:\n", "            - MFCC (mfcc)\n", "            - Chroma (chroma)\n", "            - MEL Spectrogram Frequency (mel)\n", "            - <PERSON><PERSON><PERSON> (contrast)\n", "            - Tonnetz (tonnetz)\n", "        e.g:\n", "        `features = extract_feature(path, mel=True, mfcc=True)`\n", "    \"\"\"\n", "    mfcc = kwargs.get(\"mfcc\")\n", "    chroma = kwargs.get(\"chroma\")\n", "    mel = kwargs.get(\"mel\")\n", "    contrast = kwargs.get(\"contrast\")\n", "    tonnetz = kwargs.get(\"tonnetz\")\n", "    with soundfile.SoundFile(file_name) as sound_file:\n", "        X = sound_file.read(dtype=\"float32\")\n", "        sample_rate = sound_file.samplerate\n", "        if chroma or contrast:\n", "            stft = np.abs(librosa.stft(X))\n", "        result = np.array([])\n", "        if mfcc:\n", "            mfccs = np.mean(librosa.feature.mfcc(y=X, sr=sample_rate, n_mfcc=40).T, axis=0)\n", "            result = np.hstack((result, mfccs))\n", "        if chroma:\n", "            chroma = np.mean(librosa.feature.chroma_stft(S=stft, sr=sample_rate).T,axis=0)\n", "            result = np.hstack((result, chroma))\n", "        if mel:\n", "            mel = np.mean(librosa.feature.melspectrogram(X, sr=sample_rate).T,axis=0)\n", "            result = np.hstack((result, mel))\n", "        if contrast:\n", "            contrast = np.mean(librosa.feature.spectral_contrast(S=stft, sr=sample_rate).T,axis=0)\n", "            result = np.hstack((result, contrast))\n", "        if tonnetz:\n", "            tonnetz = np.mean(librosa.feature.tonnetz(y=librosa.effects.harmonic(X), sr=sample_rate).T,axis=0)\n", "            result = np.hstack((result, tonnetz))\n", "    return result"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "PWOhBXUk_cNU", "colab_type": "code", "colab": {}}, "source": ["def load_data(test_size=0.2):\n", "    X, y = [], []\n", "    try :\n", "      for file in glob.glob(\"/content/drive/My Drive/wav/Actor_*/*.wav\"):\n", "          # get the base name of the audio file\n", "          basename = os.path.basename(file)\n", "          print(basename)\n", "          # get the emotion label\n", "          emotion = int2emotion[basename.split(\"-\")[2]]\n", "          # we allow only AVAILABLE_EMOTIONS we set\n", "          if emotion not in AVAILABLE_EMOTIONS:\n", "              continue\n", "          # extract speech features\n", "          features = extract_feature(file, mfcc=True, chroma=True, mel=True)\n", "          # add to data\n", "          X.append(features)\n", "          y.append(emotion)\n", "    except :\n", "         pass\n", "    # split the data to training and testing and return it\n", "    return train_test_split(np.array(X), y, test_size=test_size, random_state=7)"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "okb7d-c3_z4l", "colab_type": "code", "outputId": "5438af64-6c3e-4dae-dbfd-e884b29570a3", "colab": {"base_uri": "https://localhost:8080/", "height": 1000}}, "source": ["X_train, X_test, y_train, y_test = load_data(test_size=0.25)\n", "# print some details\n", "# number of samples in training data\n", "print(\"[+] Number of training samples:\", X_train.shape[0])\n", "# number of samples in testing data\n", "print(\"[+] Number of testing samples:\", X_test.shape[0])\n", "# number of features used\n", "# this is a vector of features extracted \n", "# using utils.extract_features() method\n", "print(\"[+] Number of features:\", X_train.shape[1])"], "execution_count": 0, "outputs": [{"output_type": "stream", "text": ["03-01-01-01-01-01-21.wav\n", "03-01-01-01-02-02-21.wav\n", "03-01-01-01-01-02-21.wav\n", "03-01-02-01-01-01-21.wav\n", "03-01-02-01-01-02-21.wav\n", "03-01-02-01-02-01-21.wav\n", "03-01-01-01-02-01-21.wav\n", "03-01-02-01-02-02-21.wav\n", "03-01-02-02-01-01-21.wav\n", "03-01-02-02-02-01-21.wav\n", "03-01-02-02-02-02-21.wav\n", "03-01-02-02-01-02-21.wav\n", "03-01-03-01-02-02-21.wav\n", "03-01-03-01-01-01-21.wav\n", "03-01-03-01-02-01-21.wav\n", "03-01-03-01-01-02-21.wav\n", "03-01-03-02-02-02-21.wav\n", "03-01-03-02-01-01-21.wav\n", "03-01-03-02-02-01-21.wav\n", "03-01-03-02-01-02-21.wav\n", "03-01-04-01-01-01-21.wav\n", "03-01-04-01-01-02-21.wav\n", "03-01-04-01-02-01-21.wav\n", "03-01-04-01-02-02-21.wav\n", "03-01-05-01-01-02-21.wav\n", "03-01-04-02-01-01-21.wav\n", "03-01-04-02-01-02-21.wav\n", "03-01-05-01-02-01-21.wav\n", "03-01-04-02-02-02-21.wav\n", "03-01-05-01-01-01-21.wav\n", "03-01-05-02-01-01-21.wav\n", "03-01-05-01-02-02-21.wav\n", "03-01-04-02-02-01-21.wav\n", "03-01-06-01-02-02-21.wav\n", "03-01-05-02-02-01-21.wav\n", "03-01-06-02-01-01-21.wav\n", "03-01-05-02-01-02-21.wav\n", "03-01-06-02-01-02-21.wav\n", "03-01-06-02-02-01-21.wav\n", "03-01-06-01-01-01-21.wav\n", "03-01-06-01-01-02-21.wav\n", "03-01-05-02-02-02-21.wav\n", "03-01-06-01-02-01-21.wav\n", "03-01-06-02-02-02-21.wav\n", "03-01-07-01-01-02-21.wav\n", "03-01-07-01-01-01-21.wav\n", "03-01-07-01-02-02-21.wav\n", "03-01-07-01-02-01-21.wav\n", "03-01-07-02-02-02-21.wav\n", "03-01-07-02-01-02-21.wav\n", "03-01-07-02-01-01-21.wav\n", "03-01-08-01-01-02-21.wav\n", "03-01-07-02-02-01-21.wav\n", "03-01-08-01-01-01-21.wav\n", "03-01-08-01-02-02-21.wav\n", "03-01-08-01-02-01-21.wav\n", "03-01-08-02-01-01-21.wav\n", "03-01-08-02-01-02-21.wav\n", "03-01-08-02-02-02-21.wav\n", "03-01-08-02-02-01-21.wav\n", "03-02-01-01-02-02-21.wav\n", "03-02-01-01-02-01-21.wav\n", "03-02-01-01-01-01-21.wav\n", "03-02-01-01-01-02-21.wav\n", "03-02-02-01-01-02-21.wav\n", "03-02-02-01-01-01-21.wav\n", "03-02-02-01-02-01-21.wav\n", "03-02-02-01-02-02-21.wav\n", "03-02-02-02-01-01-21.wav\n", "03-02-02-02-01-02-21.wav\n", "03-02-03-01-01-02-21.wav\n", "03-02-02-02-02-01-21.wav\n", "03-02-03-01-02-01-21.wav\n", "03-02-03-01-01-01-21.wav\n", "03-02-03-01-02-02-21.wav\n", "03-02-02-02-02-02-21.wav\n", "03-02-03-02-01-01-21.wav\n", "03-02-03-02-01-02-21.wav\n", "03-02-04-01-02-02-21.wav\n", "03-02-03-02-02-01-21.wav\n", "03-02-04-02-01-01-21.wav\n", "03-02-03-02-02-02-21.wav\n", "03-02-04-02-01-02-21.wav\n", "03-02-04-01-01-01-21.wav\n", "03-02-04-02-02-01-21.wav\n", "03-02-04-01-02-01-21.wav\n", "03-02-04-01-01-02-21.wav\n", "03-02-04-02-02-02-21.wav\n", "03-02-05-01-01-01-21.wav\n", "03-02-05-01-02-02-21.wav\n", "03-02-05-02-01-01-21.wav\n", "03-02-05-01-01-02-21.wav\n", "03-02-05-01-02-01-21.wav\n", "03-02-05-02-01-02-21.wav\n", "03-02-05-02-02-01-21.wav\n", "03-02-05-02-02-02-21.wav\n", "03-02-06-01-02-01-21.wav\n", "03-02-06-01-01-01-21.wav\n", "03-02-06-01-01-02-21.wav\n", "03-02-06-02-01-01-21.wav\n", "03-02-06-01-02-02-21.wav\n", "03-02-06-02-02-01-21.wav\n", "03-02-06-02-01-02-21.wav\n", "03-02-06-02-02-02-21.wav\n", "03-01-01-01-01-01-16.wav\n", "03-01-01-01-01-02-16.wav\n", "03-01-01-01-02-02-16.wav\n", "03-01-01-01-02-01-16.wav\n", "03-01-02-01-01-01-16.wav\n", "03-01-02-01-01-02-16.wav\n", "03-01-02-01-02-01-16.wav\n", "03-01-02-01-02-02-16.wav\n", "03-01-02-02-02-01-16.wav\n", "03-01-02-02-01-02-16.wav\n", "03-01-02-02-02-02-16.wav\n", "03-01-02-02-01-01-16.wav\n", "03-01-03-01-01-02-16.wav\n", "03-01-03-01-01-01-16.wav\n", "03-01-03-01-02-01-16.wav\n", "03-01-03-01-02-02-16.wav\n", "03-01-03-02-01-01-16.wav\n", "03-01-04-01-01-01-16.wav\n", "03-01-03-02-02-01-16.wav\n", "03-01-04-01-01-02-16.wav\n", "03-01-03-02-02-02-16.wav\n", "03-01-03-02-01-02-16.wav\n", "03-01-04-01-02-01-16.wav\n", "03-01-04-02-01-01-16.wav\n", "03-01-04-01-02-02-16.wav\n", "03-01-04-02-01-02-16.wav\n", "03-01-04-02-02-01-16.wav\n", "03-01-04-02-02-02-16.wav\n", "03-01-05-01-02-01-16.wav\n", "03-01-05-01-01-01-16.wav\n", "03-01-05-01-02-02-16.wav\n", "03-01-05-01-01-02-16.wav\n", "03-01-05-02-01-01-16.wav\n", "03-01-05-02-01-02-16.wav\n", "03-01-05-02-02-01-16.wav\n", "03-01-05-02-02-02-16.wav\n", "03-01-06-01-01-01-16.wav\n", "03-01-06-02-01-01-16.wav\n", "03-01-06-01-02-01-16.wav\n", "03-01-06-01-01-02-16.wav\n", "03-01-06-02-01-02-16.wav\n", "03-01-06-01-02-02-16.wav\n", "03-01-06-02-02-01-16.wav\n", "03-01-06-02-02-02-16.wav\n", "03-01-07-01-01-01-16.wav\n", "03-01-07-01-01-02-16.wav\n", "03-01-08-01-01-02-16.wav\n", "03-01-07-01-02-02-16.wav\n", "03-01-07-01-02-01-16.wav\n", "03-01-07-02-02-01-16.wav\n", "03-01-07-02-01-02-16.wav\n", "03-01-07-02-01-01-16.wav\n", "03-01-08-01-01-01-16.wav\n", "03-01-07-02-02-02-16.wav\n", "03-01-08-01-02-01-16.wav\n", "03-02-01-01-01-01-16.wav\n", "03-01-08-01-02-02-16.wav\n", "03-02-01-01-01-02-16.wav\n", "03-01-08-02-01-01-16.wav\n", "03-01-08-02-01-02-16.wav\n", "03-01-08-02-02-01-16.wav\n", "03-01-08-02-02-02-16.wav\n", "03-02-01-01-02-01-16.wav\n", "03-02-01-01-02-02-16.wav\n", "03-02-02-01-01-01-16.wav\n", "03-02-02-02-02-01-16.wav\n", "03-02-02-01-01-02-16.wav\n", "03-02-02-02-02-02-16.wav\n", "03-02-02-01-02-01-16.wav\n", "03-02-02-01-02-02-16.wav\n", "03-02-03-01-01-02-16.wav\n", "03-02-03-01-01-01-16.wav\n", "03-02-02-02-01-02-16.wav\n", "03-02-02-02-01-01-16.wav\n", "03-02-03-01-02-01-16.wav\n", "03-02-03-02-02-01-16.wav\n", "03-02-03-01-02-02-16.wav\n", "03-02-03-02-02-02-16.wav\n", "03-02-03-02-01-01-16.wav\n", "03-02-04-01-01-01-16.wav\n", "03-02-03-02-01-02-16.wav\n", "03-02-04-01-01-02-16.wav\n", "03-02-04-01-02-01-16.wav\n", "03-02-04-01-02-02-16.wav\n", "03-02-04-02-01-01-16.wav\n", "03-02-04-02-02-01-16.wav\n", "03-02-04-02-01-02-16.wav\n", "03-02-05-01-01-01-16.wav\n", "03-02-04-02-02-02-16.wav\n", "03-02-05-01-01-02-16.wav\n", "03-02-05-01-02-01-16.wav\n", "03-02-05-01-02-02-16.wav\n", "03-02-05-02-01-02-16.wav\n", "03-02-05-02-02-01-16.wav\n", "03-02-05-02-01-01-16.wav\n", "03-02-05-02-02-02-16.wav\n", "03-02-06-01-01-01-16.wav\n", "03-02-06-01-01-02-16.wav\n", "03-02-06-01-02-01-16.wav\n", "03-02-06-02-01-01-16.wav\n", "03-02-06-01-02-02-16.wav\n", "03-02-06-02-01-02-16.wav\n", "03-02-06-02-02-01-16.wav\n", "03-02-06-02-02-02-16.wav\n", "03-01-01-01-01-01-23.wav\n", "03-01-01-01-01-02-23.wav\n", "03-01-01-01-02-01-23.wav\n", "03-01-01-01-02-02-23.wav\n", "03-01-02-01-01-01-23.wav\n", "03-01-02-01-01-02-23.wav\n", "03-01-02-01-02-02-23.wav\n", "03-01-02-01-02-01-23.wav\n", "03-01-02-02-02-01-23.wav\n", "03-01-02-02-01-01-23.wav\n", "03-01-02-02-01-02-23.wav\n", "03-01-03-01-01-01-23.wav\n", "03-01-02-02-02-02-23.wav\n", "03-01-03-01-01-02-23.wav\n", "03-01-03-01-02-02-23.wav\n", "03-01-03-01-02-01-23.wav\n", "03-01-03-02-01-01-23.wav\n", "03-01-03-02-01-02-23.wav\n", "03-01-03-02-02-01-23.wav\n", "03-01-04-01-01-01-23.wav\n", "03-01-03-02-02-02-23.wav\n", "03-01-04-01-02-02-23.wav\n", "03-01-04-01-02-01-23.wav\n", "03-01-04-01-01-02-23.wav\n", "03-01-04-02-02-01-23.wav\n", "03-01-04-02-01-02-23.wav\n", "03-01-04-02-01-01-23.wav\n", "03-01-05-01-01-01-23.wav\n", "03-01-05-02-01-02-23.wav\n", "03-01-04-02-02-02-23.wav\n", "03-01-05-01-01-02-23.wav\n", "03-01-05-02-02-02-23.wav\n", "03-01-05-02-02-01-23.wav\n", "03-01-05-01-02-01-23.wav\n", "03-01-05-02-01-01-23.wav\n", "03-01-05-01-02-02-23.wav\n", "03-01-06-01-01-01-23.wav\n", "03-01-06-01-01-02-23.wav\n", "03-01-06-02-02-02-23.wav\n", "03-01-06-01-02-01-23.wav\n", "03-01-06-01-02-02-23.wav\n", "03-01-06-02-01-01-23.wav\n", "03-01-07-01-01-01-23.wav\n", "03-01-07-01-01-02-23.wav\n", "03-01-06-02-02-01-23.wav\n", "03-01-06-02-01-02-23.wav\n", "03-01-07-01-02-01-23.wav\n", "03-01-07-01-02-02-23.wav\n", "03-01-07-02-01-01-23.wav\n", "03-01-07-02-01-02-23.wav\n", "03-01-07-02-02-01-23.wav\n", "03-01-07-02-02-02-23.wav\n", "03-01-08-01-01-02-23.wav\n", "03-01-08-01-01-01-23.wav\n", "03-01-08-01-02-01-23.wav\n", "03-01-08-01-02-02-23.wav\n", "03-02-01-01-01-02-23.wav\n", "03-01-08-02-01-01-23.wav\n", "03-01-08-02-02-02-23.wav\n", "03-02-01-01-02-01-23.wav\n", "03-01-08-02-01-02-23.wav\n", "03-02-01-01-02-02-23.wav\n", "03-01-08-02-02-01-23.wav\n", "03-02-01-01-01-01-23.wav\n", "03-02-02-01-01-01-23.wav\n", "03-02-02-01-01-02-23.wav\n", "03-02-02-02-02-02-23.wav\n", "03-02-02-01-02-01-23.wav\n", "03-02-03-01-01-01-23.wav\n", "03-02-02-01-02-02-23.wav\n", "03-02-03-01-01-02-23.wav\n", "03-02-02-02-01-01-23.wav\n", "03-02-03-01-02-01-23.wav\n", "03-02-02-02-01-02-23.wav\n", "03-02-02-02-02-01-23.wav\n", "03-02-03-01-02-02-23.wav\n", "03-02-03-02-01-01-23.wav\n", "03-02-03-02-02-02-23.wav\n", "03-02-04-01-01-01-23.wav\n", "03-02-04-01-01-02-23.wav\n", "03-02-04-01-02-01-23.wav\n", "03-02-04-01-02-02-23.wav\n", "03-02-04-02-01-01-23.wav\n", "03-02-03-02-01-02-23.wav\n", "03-02-03-02-02-01-23.wav\n", "03-02-04-02-01-02-23.wav\n", "03-02-04-02-02-01-23.wav\n", "03-02-05-01-02-02-23.wav\n", "03-02-04-02-02-02-23.wav\n", "03-02-05-02-01-01-23.wav\n", "03-02-05-01-01-01-23.wav\n", "03-02-05-02-01-02-23.wav\n", "03-02-05-01-02-01-23.wav\n", "03-02-05-02-02-01-23.wav\n", "03-02-05-01-01-02-23.wav\n", "03-02-05-02-02-02-23.wav\n", "03-02-06-01-01-01-23.wav\n", "03-02-06-01-02-02-23.wav\n", "03-02-06-01-01-02-23.wav\n", "03-02-06-02-01-01-23.wav\n", "03-02-06-01-02-01-23.wav\n", "03-02-06-02-01-02-23.wav\n", "03-02-06-02-02-01-23.wav\n", "03-02-06-02-02-02-23.wav\n", "03-01-01-01-01-01-19.wav\n", "03-01-01-01-01-02-19.wav\n", "03-01-01-01-02-01-19.wav\n", "03-01-02-02-01-02-19.wav\n", "03-01-01-01-02-02-19.wav\n", "03-01-02-02-01-01-19.wav\n", "03-01-02-01-01-01-19.wav\n", "03-01-02-02-02-01-19.wav\n", "03-01-02-01-02-01-19.wav\n", "03-01-02-01-01-02-19.wav\n", "03-01-02-02-02-02-19.wav\n", "03-01-02-01-02-02-19.wav\n", "03-01-03-01-01-01-19.wav\n", "03-01-03-02-01-01-19.wav\n", "03-01-03-01-01-02-19.wav\n", "03-01-03-01-02-01-19.wav\n", "03-01-03-02-01-02-19.wav\n", "03-01-03-01-02-02-19.wav\n", "03-01-03-02-02-01-19.wav\n", "03-01-03-02-02-02-19.wav\n", "03-01-04-01-01-01-19.wav\n", "03-01-04-01-02-01-19.wav\n", "03-01-04-01-01-02-19.wav\n", "03-01-04-01-02-02-19.wav\n", "03-01-04-02-01-01-19.wav\n", "03-01-04-02-01-02-19.wav\n", "03-01-04-02-02-01-19.wav\n", "03-01-04-02-02-02-19.wav\n", "03-01-05-01-01-01-19.wav\n", "03-01-05-02-01-01-19.wav\n", "03-01-05-01-02-01-19.wav\n", "03-01-05-02-01-02-19.wav\n", "03-01-05-01-01-02-19.wav\n", "03-01-05-02-02-01-19.wav\n", "03-01-05-01-02-02-19.wav\n", "03-01-05-02-02-02-19.wav\n", "03-01-06-01-01-01-19.wav\n", "03-01-06-02-02-01-19.wav\n", "03-01-06-01-01-02-19.wav\n", "03-01-06-02-02-02-19.wav\n", "03-01-06-01-02-02-19.wav\n", "03-01-07-01-01-01-19.wav\n", "03-01-06-02-01-01-19.wav\n", "03-01-07-01-01-02-19.wav\n", "03-01-06-02-01-02-19.wav\n", "03-01-06-01-02-01-19.wav\n", "03-01-07-01-02-01-19.wav\n", "03-01-07-01-02-02-19.wav\n", "03-01-07-02-01-01-19.wav\n", "03-01-07-02-02-01-19.wav\n", "03-01-07-02-01-02-19.wav\n", "03-01-08-01-01-01-19.wav\n", "03-01-07-02-02-02-19.wav\n", "03-01-08-01-02-02-19.wav\n", "03-01-08-01-01-02-19.wav\n", "03-01-08-02-01-01-19.wav\n", "03-01-08-01-02-01-19.wav\n", "03-01-08-02-02-01-19.wav\n", "03-01-08-02-01-02-19.wav\n", "03-01-08-02-02-02-19.wav\n", "03-02-01-01-01-02-19.wav\n", "03-02-01-01-02-01-19.wav\n", "03-02-01-01-01-01-19.wav\n", "03-02-02-01-02-02-19.wav\n", "03-02-02-01-01-01-19.wav\n", "03-02-01-01-02-02-19.wav\n", "03-02-02-01-02-01-19.wav\n", "03-02-02-01-01-02-19.wav\n", "03-02-02-02-01-01-19.wav\n", "03-02-02-02-01-02-19.wav\n", "03-02-02-02-02-01-19.wav\n", "03-02-02-02-02-02-19.wav\n", "03-02-03-02-01-01-19.wav\n", "03-02-03-01-01-01-19.wav\n", "03-02-03-01-01-02-19.wav\n", "03-02-03-01-02-02-19.wav\n", "03-02-03-02-01-02-19.wav\n", "03-02-03-02-02-02-19.wav\n", "03-02-03-02-02-01-19.wav\n", "03-02-03-01-02-01-19.wav\n", "03-02-04-01-01-01-19.wav\n", "03-02-04-01-01-02-19.wav\n", "03-02-04-02-01-01-19.wav\n", "03-02-04-01-02-01-19.wav\n", "03-02-04-02-01-02-19.wav\n", "03-02-04-01-02-02-19.wav\n", "03-02-04-02-02-01-19.wav\n", "03-02-05-01-02-01-19.wav\n", "03-02-04-02-02-02-19.wav\n", "03-02-05-01-02-02-19.wav\n", "03-02-05-01-01-01-19.wav\n", "03-02-05-02-01-01-19.wav\n", "03-02-05-01-01-02-19.wav\n", "03-02-05-02-01-02-19.wav\n", "03-02-05-02-02-01-19.wav\n", "03-02-06-02-01-02-19.wav\n", "03-02-06-01-01-01-19.wav\n", "03-02-06-02-01-01-19.wav\n", "03-02-06-02-02-01-19.wav\n", "03-02-05-02-02-02-19.wav\n", "03-02-06-02-02-02-19.wav\n", "03-02-06-01-01-02-19.wav\n", "03-02-06-01-02-01-19.wav\n", "03-02-06-01-02-02-19.wav\n", "03-01-01-01-02-02-20.wav\n", "03-01-02-01-01-01-20.wav\n", "03-01-01-01-01-01-20.wav\n", "03-01-02-01-01-02-20.wav\n", "03-01-01-01-02-01-20.wav\n", "03-01-02-01-02-01-20.wav\n", "03-01-01-01-01-02-20.wav\n", "03-01-02-01-02-02-20.wav\n", "03-01-02-02-02-02-20.wav\n", "03-01-02-02-01-01-20.wav\n", "03-01-03-01-01-01-20.wav\n", "03-01-02-02-01-02-20.wav\n", "03-01-03-01-01-02-20.wav\n", "03-01-02-02-02-01-20.wav\n", "03-01-03-01-02-02-20.wav\n", "03-01-03-02-01-02-20.wav\n", "03-01-03-02-01-01-20.wav\n", "03-01-03-02-02-01-20.wav\n", "03-01-03-02-02-02-20.wav\n", "03-01-04-01-01-01-20.wav\n", "03-01-04-01-02-01-20.wav\n", "03-01-04-01-01-02-20.wav\n", "03-01-04-01-02-02-20.wav\n", "03-01-04-02-01-01-20.wav\n", "03-01-04-02-02-02-20.wav\n", "03-01-04-02-02-01-20.wav\n", "03-01-05-01-01-01-20.wav\n", "03-01-04-02-01-02-20.wav\n", "03-01-05-01-01-02-20.wav\n", "03-01-05-01-02-01-20.wav\n", "03-01-05-01-02-02-20.wav\n", "03-01-05-02-01-01-20.wav\n", "03-01-05-02-01-02-20.wav\n", "03-01-05-02-02-01-20.wav\n", "03-01-06-01-02-02-20.wav\n", "03-01-06-01-01-01-20.wav\n", "03-01-05-02-02-02-20.wav\n", "03-01-06-01-02-01-20.wav\n", "03-01-06-02-01-01-20.wav\n", "03-01-06-01-01-02-20.wav\n", "03-01-06-02-01-02-20.wav\n", "03-01-06-02-02-01-20.wav\n", "03-01-06-02-02-02-20.wav\n", "03-01-07-01-01-01-20.wav\n", "03-01-07-02-01-01-20.wav\n", "03-01-07-01-01-02-20.wav\n", "03-01-07-01-02-01-20.wav\n", "03-01-07-02-01-02-20.wav\n", "03-01-07-01-02-02-20.wav\n", "03-01-08-01-01-01-20.wav\n", "03-01-07-02-02-02-20.wav\n", "03-01-07-02-02-01-20.wav\n", "03-01-08-01-01-02-20.wav\n", "03-01-08-01-02-01-20.wav\n", "03-01-08-02-01-02-20.wav\n", "03-01-08-01-02-02-20.wav\n", "03-01-08-02-01-01-20.wav\n", "03-01-08-02-02-01-20.wav\n", "03-02-01-01-01-01-20.wav\n", "03-01-08-02-02-02-20.wav\n", "03-02-01-01-01-02-20.wav\n", "03-02-01-01-02-01-20.wav\n", "03-02-01-01-02-02-20.wav\n", "03-02-02-02-01-01-20.wav\n", "03-02-02-01-01-01-20.wav\n", "03-02-02-01-01-02-20.wav\n", "03-02-02-02-01-02-20.wav\n", "03-02-02-01-02-02-20.wav\n", "03-02-02-02-02-01-20.wav\n", "03-02-02-01-02-01-20.wav\n", "03-02-02-02-02-02-20.wav\n", "03-02-03-01-01-01-20.wav\n", "03-02-03-01-01-02-20.wav\n", "03-02-03-02-02-01-20.wav\n", "03-02-03-01-02-01-20.wav\n", "03-02-04-01-01-01-20.wav\n", "03-02-03-01-02-02-20.wav\n", "03-02-03-02-02-02-20.wav\n", "03-02-03-02-01-02-20.wav\n", "03-02-04-01-01-02-20.wav\n", "03-02-03-02-01-01-20.wav\n", "03-02-04-01-02-01-20.wav\n", "03-02-04-01-02-02-20.wav\n", "03-02-04-02-02-01-20.wav\n", "03-02-04-02-01-01-20.wav\n", "03-02-04-02-02-02-20.wav\n", "03-02-04-02-01-02-20.wav\n", "03-02-05-01-01-01-20.wav\n", "03-02-05-01-02-01-20.wav\n", "03-02-05-01-01-02-20.wav\n", "03-02-05-02-01-01-20.wav\n", "03-02-05-01-02-02-20.wav\n", "03-02-05-02-02-01-20.wav\n", "03-02-05-02-01-02-20.wav\n", "03-02-05-02-02-02-20.wav\n", "03-02-06-01-01-01-20.wav\n", "03-02-06-01-01-02-20.wav\n", "03-02-06-01-02-01-20.wav\n", "03-02-06-02-01-02-20.wav\n", "03-02-06-02-02-01-20.wav\n", "03-02-06-01-02-02-20.wav\n", "03-02-06-02-02-02-20.wav\n", "03-02-06-02-01-01-20.wav\n", "03-01-01-01-01-01-15.wav\n", "03-01-01-01-01-02-15.wav\n", "03-01-01-01-02-01-15.wav\n", "03-01-01-01-02-02-15.wav\n", "03-01-02-01-01-02-15.wav\n", "03-01-02-02-02-01-15.wav\n", "03-01-02-01-02-01-15.wav\n", "03-01-02-01-01-01-15.wav\n", "03-01-03-01-01-01-15.wav\n", "03-01-02-02-01-02-15.wav\n", "03-01-02-02-01-01-15.wav\n", "03-01-02-01-02-02-15.wav\n", "03-01-02-02-02-02-15.wav\n", "03-01-03-01-01-02-15.wav\n", "03-01-03-01-02-01-15.wav\n", "03-01-03-01-02-02-15.wav\n", "03-01-03-02-01-01-15.wav\n", "03-01-04-01-01-01-15.wav\n", "03-01-03-02-01-02-15.wav\n", "03-01-03-02-02-01-15.wav\n", "03-01-03-02-02-02-15.wav\n", "03-01-04-01-02-01-15.wav\n", "03-01-04-01-02-02-15.wav\n", "03-01-04-01-01-02-15.wav\n", "03-01-04-02-01-01-15.wav\n", "03-01-05-01-02-01-15.wav\n", "03-01-04-02-01-02-15.wav\n", "03-01-05-01-02-02-15.wav\n", "03-01-04-02-02-02-15.wav\n", "03-01-04-02-02-01-15.wav\n", "03-01-05-02-01-01-15.wav\n", "03-01-05-02-01-02-15.wav\n", "03-01-05-01-01-01-15.wav\n", "03-01-05-01-01-02-15.wav\n", "03-01-05-02-02-01-15.wav\n", "03-01-05-02-02-02-15.wav\n", "03-01-06-01-01-01-15.wav\n", "03-01-06-02-01-01-15.wav\n", "03-01-06-01-02-01-15.wav\n", "03-01-06-02-01-02-15.wav\n", "03-01-06-01-01-02-15.wav\n", "03-01-06-01-02-02-15.wav\n", "03-01-06-02-02-02-15.wav\n", "03-01-06-02-02-01-15.wav\n", "03-01-07-01-01-01-15.wav\n", "03-01-07-01-01-02-15.wav\n", "03-01-07-01-02-01-15.wav\n", "03-01-07-02-01-01-15.wav\n", "03-01-07-01-02-02-15.wav\n", "03-01-07-02-01-02-15.wav\n", "03-01-07-02-02-01-15.wav\n", "03-01-08-01-01-01-15.wav\n", "03-01-08-01-01-02-15.wav\n", "03-01-07-02-02-02-15.wav\n", "03-01-08-01-02-01-15.wav\n", "03-01-08-02-01-02-15.wav\n", "03-01-08-01-02-02-15.wav\n", "03-01-08-02-01-01-15.wav\n", "03-01-08-02-02-01-15.wav\n", "03-02-01-01-01-02-15.wav\n", "03-02-01-01-01-01-15.wav\n", "03-01-08-02-02-02-15.wav\n", "03-02-02-01-01-01-15.wav\n", "03-02-01-01-02-02-15.wav\n", "03-02-01-01-02-01-15.wav\n", "03-02-02-01-02-01-15.wav\n", "03-02-02-01-01-02-15.wav\n", "03-02-02-01-02-02-15.wav\n", "03-02-02-02-01-01-15.wav\n", "03-02-03-01-02-01-15.wav\n", "03-02-02-02-02-01-15.wav\n", "03-02-02-02-01-02-15.wav\n", "03-02-03-01-02-02-15.wav\n", "03-02-02-02-02-02-15.wav\n", "03-02-03-01-01-01-15.wav\n", "03-02-03-02-01-02-15.wav\n", "03-02-03-02-01-01-15.wav\n", "03-02-03-01-01-02-15.wav\n", "03-02-03-02-02-01-15.wav\n", "03-02-04-01-02-01-15.wav\n", "03-02-03-02-02-02-15.wav\n", "03-02-04-01-02-02-15.wav\n", "03-02-04-01-01-01-15.wav\n", "03-02-04-01-01-02-15.wav\n", "03-02-04-02-02-02-15.wav\n", "03-02-04-02-01-02-15.wav\n", "03-02-04-02-01-01-15.wav\n", "03-02-04-02-02-01-15.wav\n", "03-02-05-01-01-01-15.wav\n", "03-02-05-01-01-02-15.wav\n", "03-02-05-01-02-01-15.wav\n", "03-02-05-02-01-01-15.wav\n", "03-02-05-01-02-02-15.wav\n", "03-02-05-02-01-02-15.wav\n", "03-02-05-02-02-01-15.wav\n", "03-02-05-02-02-02-15.wav\n", "03-02-06-01-01-01-15.wav\n", "03-02-06-01-01-02-15.wav\n", "03-02-06-01-02-01-15.wav\n", "03-02-06-02-01-01-15.wav\n", "03-02-06-01-02-02-15.wav\n", "03-02-06-02-02-01-15.wav\n", "03-02-06-02-01-02-15.wav\n", "03-02-06-02-02-02-15.wav\n", "03-01-01-01-02-01-17.wav\n", "03-01-01-01-01-01-17.wav\n", "03-01-01-01-02-02-17.wav\n", "03-01-01-01-01-02-17.wav\n", "03-01-02-01-01-01-17.wav\n", "03-01-02-01-02-01-17.wav\n", "03-01-02-02-01-01-17.wav\n", "03-01-02-01-01-02-17.wav\n", "03-01-02-02-01-02-17.wav\n", "03-01-02-01-02-02-17.wav\n", "03-01-02-02-02-01-17.wav\n", "03-01-02-02-02-02-17.wav\n", "03-01-03-01-01-01-17.wav\n", "03-01-03-01-01-02-17.wav\n", "03-01-03-01-02-01-17.wav\n", "03-01-03-02-02-01-17.wav\n", "03-01-03-01-02-02-17.wav\n", "03-01-03-02-02-02-17.wav\n", "03-01-03-02-01-01-17.wav\n", "03-01-04-01-01-01-17.wav\n", "03-01-03-02-01-02-17.wav\n", "03-01-04-01-01-02-17.wav\n", "03-01-04-01-02-01-17.wav\n", "03-01-05-01-01-01-17.wav\n", "03-01-04-01-02-02-17.wav\n", "03-01-05-01-01-02-17.wav\n", "03-01-04-02-01-02-17.wav\n", "03-01-04-02-01-01-17.wav\n", "03-01-05-01-02-01-17.wav\n", "03-01-05-01-02-02-17.wav\n", "03-01-04-02-02-02-17.wav\n", "03-01-04-02-02-01-17.wav\n", "03-01-05-02-01-01-17.wav\n", "03-01-05-02-02-01-17.wav\n", "03-01-05-02-01-02-17.wav\n", "03-01-05-02-02-02-17.wav\n", "03-01-06-01-01-01-17.wav\n", "03-01-06-01-02-01-17.wav\n", "03-01-06-01-01-02-17.wav\n", "03-01-06-01-02-02-17.wav\n", "03-01-06-02-02-01-17.wav\n", "03-01-06-02-01-02-17.wav\n", "03-01-06-02-01-01-17.wav\n", "03-01-06-02-02-02-17.wav\n", "03-01-07-01-01-01-17.wav\n", "03-01-07-01-01-02-17.wav\n", "03-01-07-01-02-01-17.wav\n", "03-01-08-01-01-01-17.wav\n", "03-01-07-02-01-02-17.wav\n", "03-01-07-02-02-01-17.wav\n", "03-01-07-01-02-02-17.wav\n", "03-01-07-02-01-01-17.wav\n", "03-01-08-01-01-02-17.wav\n", "03-01-07-02-02-02-17.wav\n", "03-01-08-01-02-02-17.wav\n", "03-01-08-01-02-01-17.wav\n", "03-01-08-02-01-01-17.wav\n", "03-02-01-01-02-01-17.wav\n", "03-01-08-02-01-02-17.wav\n", "03-01-08-02-02-01-17.wav\n", "03-02-01-01-02-02-17.wav\n", "03-02-02-01-01-01-17.wav\n", "03-02-02-01-01-02-17.wav\n", "03-02-02-01-02-01-17.wav\n", "03-02-02-02-01-01-17.wav\n", "03-02-02-01-02-02-17.wav\n", "03-02-02-02-01-02-17.wav\n", "03-01-08-02-02-02-17.wav\n", "03-02-01-01-01-01-17.wav\n", "03-02-01-01-01-02-17.wav\n", "03-02-02-02-02-01-17.wav\n", "03-02-03-01-01-01-17.wav\n", "03-02-02-02-02-02-17.wav\n", "03-02-03-01-01-02-17.wav\n", "03-02-03-01-02-02-17.wav\n", "03-02-03-01-02-01-17.wav\n", "03-02-03-02-01-01-17.wav\n", "03-02-04-01-01-01-17.wav\n", "03-02-03-02-02-01-17.wav\n", "03-02-04-01-01-02-17.wav\n", "03-02-03-02-01-02-17.wav\n", "03-02-03-02-02-02-17.wav\n", "03-02-04-01-02-02-17.wav\n", "03-02-04-01-02-01-17.wav\n", "03-02-04-02-01-01-17.wav\n", "03-02-04-02-01-02-17.wav\n", "03-02-05-01-02-01-17.wav\n", "03-02-04-02-02-01-17.wav\n", "03-02-05-01-02-02-17.wav\n", "03-02-04-02-02-02-17.wav\n", "03-02-05-02-01-02-17.wav\n", "03-02-05-02-01-01-17.wav\n", "03-02-05-01-01-01-17.wav\n", "03-02-05-01-01-02-17.wav\n", "03-02-05-02-02-01-17.wav\n", "03-02-06-02-01-01-17.wav\n", "03-02-05-02-02-02-17.wav\n", "03-02-06-02-01-02-17.wav\n", "03-02-06-01-01-01-17.wav\n", "03-02-06-01-02-01-17.wav\n", "03-02-06-01-01-02-17.wav\n", "03-02-06-02-02-01-17.wav\n", "03-02-06-01-02-02-17.wav\n", "03-02-06-02-02-02-17.wav\n", "03-01-01-01-01-02-22.wav\n", "03-01-01-01-01-01-22.wav\n", "03-01-01-01-02-01-22.wav\n", "03-01-01-01-02-02-22.wav\n", "03-01-02-01-01-01-22.wav\n", "03-01-02-01-01-02-22.wav\n", "03-01-02-01-02-02-22.wav\n", "03-01-02-01-02-01-22.wav\n", "03-01-02-02-01-01-22.wav\n", "03-01-02-02-01-02-22.wav\n", "03-01-02-02-02-02-22.wav\n", "03-01-03-01-01-01-22.wav\n", "03-01-02-02-02-01-22.wav\n", "03-01-03-01-02-01-22.wav\n", "03-01-03-01-01-02-22.wav\n", "03-01-03-01-02-02-22.wav\n", "03-01-04-01-01-02-22.wav\n", "03-01-03-02-01-01-22.wav\n", "03-01-04-01-02-01-22.wav\n", "03-01-03-02-01-02-22.wav\n", "03-01-04-01-02-02-22.wav\n", "03-01-04-01-01-01-22.wav\n", "03-01-03-02-02-01-22.wav\n", "03-01-04-02-01-01-22.wav\n", "03-01-03-02-02-02-22.wav\n", "03-01-04-02-01-02-22.wav\n", "03-01-05-01-01-02-22.wav\n", "03-01-04-02-02-01-22.wav\n", "03-01-05-01-02-01-22.wav\n", "03-01-04-02-02-02-22.wav\n", "03-01-05-01-02-02-22.wav\n", "03-01-05-01-01-01-22.wav\n", "03-01-05-02-01-01-22.wav\n", "03-01-05-02-01-02-22.wav\n", "03-01-05-02-02-01-22.wav\n", "03-01-06-01-01-01-22.wav\n", "03-01-06-01-01-02-22.wav\n", "03-01-05-02-02-02-22.wav\n", "03-01-06-01-02-02-22.wav\n", "03-01-06-01-02-01-22.wav\n", "03-01-06-02-01-01-22.wav\n", "03-01-06-02-01-02-22.wav\n", "03-01-06-02-02-02-22.wav\n", "03-01-06-02-02-01-22.wav\n", "03-01-07-01-01-02-22.wav\n", "03-01-07-01-01-01-22.wav\n", "03-01-07-01-02-02-22.wav\n", "03-01-07-01-02-01-22.wav\n", "03-01-07-02-01-01-22.wav\n", "03-01-07-02-02-02-22.wav\n", "03-01-07-02-01-02-22.wav\n", "03-01-07-02-02-01-22.wav\n", "03-01-08-01-02-02-22.wav\n", "03-01-08-01-01-01-22.wav\n", "03-01-08-02-01-01-22.wav\n", "03-01-08-01-01-02-22.wav\n", "03-01-08-02-01-02-22.wav\n", "03-01-08-01-02-01-22.wav\n", "03-01-08-02-02-01-22.wav\n", "03-01-08-02-02-02-22.wav\n", "03-02-02-01-01-02-22.wav\n", "03-02-01-01-01-01-22.wav\n", "03-02-02-01-02-01-22.wav\n", "03-02-01-01-01-02-22.wav\n", "03-02-02-01-02-02-22.wav\n", "03-02-01-01-02-02-22.wav\n", "03-02-02-02-01-01-22.wav\n", "03-02-01-01-02-01-22.wav\n", "03-02-02-01-01-01-22.wav\n", "03-02-02-02-01-02-22.wav\n", "03-02-02-02-02-02-22.wav\n", "03-02-03-01-01-02-22.wav\n", "03-02-02-02-02-01-22.wav\n", "03-02-03-01-02-01-22.wav\n", "03-02-03-01-01-01-22.wav\n", "03-02-03-01-02-02-22.wav\n", "03-02-03-02-01-01-22.wav\n", "03-02-03-02-02-01-22.wav\n", "03-02-03-02-01-02-22.wav\n", "03-02-03-02-02-02-22.wav\n", "03-02-04-01-02-01-22.wav\n", "03-02-04-01-01-02-22.wav\n", "03-02-04-01-01-01-22.wav\n", "03-02-04-01-02-02-22.wav\n", "03-02-04-02-01-02-22.wav\n", "03-02-04-02-01-01-22.wav\n", "03-02-05-01-01-02-22.wav\n", "03-02-04-02-02-01-22.wav\n", "03-02-05-01-02-01-22.wav\n", "03-02-04-02-02-02-22.wav\n", "03-02-05-02-01-01-22.wav\n", "03-02-05-01-02-02-22.wav\n", "03-02-05-01-01-01-22.wav\n", "03-02-05-02-01-02-22.wav\n", "03-02-06-01-01-02-22.wav\n", "03-02-05-02-02-01-22.wav\n", "03-02-06-01-02-02-22.wav\n", "03-02-06-01-02-01-22.wav\n", "03-02-05-02-02-02-22.wav\n", "03-02-06-01-01-01-22.wav\n", "03-02-06-02-02-01-22.wav\n", "03-02-06-02-01-02-22.wav\n", "03-02-06-02-01-01-22.wav\n", "03-02-06-02-02-02-22.wav\n", "03-01-01-01-01-01-24.wav\n", "03-01-02-01-02-01-24.wav\n", "03-01-01-01-01-02-24.wav\n", "03-01-02-01-02-02-24.wav\n", "03-01-01-01-02-01-24.wav\n", "03-01-02-02-01-01-24.wav\n", "03-01-01-01-02-02-24.wav\n", "03-01-02-02-01-02-24.wav\n", "03-01-02-01-01-01-24.wav\n", "03-01-02-01-01-02-24.wav\n", "03-01-02-02-02-01-24.wav\n", "03-01-02-02-02-02-24.wav\n", "03-01-03-01-01-01-24.wav\n", "03-01-03-01-02-01-24.wav\n", "03-01-03-01-01-02-24.wav\n", "03-01-03-01-02-02-24.wav\n", "03-01-03-02-01-01-24.wav\n", "03-01-03-02-01-02-24.wav\n", "03-01-03-02-02-02-24.wav\n", "03-01-03-02-02-01-24.wav\n", "03-01-04-01-01-01-24.wav\n", "03-01-04-01-02-01-24.wav\n", "03-01-04-01-01-02-24.wav\n", "03-01-04-02-01-01-24.wav\n", "03-01-04-01-02-02-24.wav\n", "03-01-04-02-02-01-24.wav\n", "03-01-04-02-01-02-24.wav\n", "03-01-04-02-02-02-24.wav\n", "03-01-05-01-02-01-24.wav\n", "03-01-05-01-01-01-24.wav\n", "03-01-05-01-01-02-24.wav\n", "03-01-05-01-02-02-24.wav\n", "03-01-05-02-01-01-24.wav\n", "03-01-05-02-01-02-24.wav\n", "03-01-05-02-02-01-24.wav\n", "03-01-05-02-02-02-24.wav\n", "03-01-06-02-01-01-24.wav\n", "03-01-06-01-01-01-24.wav\n", "03-01-06-01-01-02-24.wav\n", "03-01-06-02-01-02-24.wav\n", "03-01-06-01-02-02-24.wav\n", "03-01-06-01-02-01-24.wav\n", "03-01-06-02-02-02-24.wav\n", "03-01-06-02-02-01-24.wav\n", "03-01-07-01-01-01-24.wav\n", "03-01-07-02-02-01-24.wav\n", "03-01-07-01-01-02-24.wav\n", "03-01-07-01-02-02-24.wav\n", "03-01-07-01-02-01-24.wav\n", "03-01-07-02-02-02-24.wav\n", "03-01-08-01-01-01-24.wav\n", "03-01-07-02-01-01-24.wav\n", "03-01-07-02-01-02-24.wav\n", "03-01-08-01-01-02-24.wav\n", "03-01-08-01-02-01-24.wav\n", "03-01-08-01-02-02-24.wav\n", "03-01-08-02-02-01-24.wav\n", "03-01-08-02-01-01-24.wav\n", "03-01-08-02-02-02-24.wav\n", "03-02-01-01-02-01-24.wav\n", "03-02-01-01-01-02-24.wav\n", "03-01-08-02-01-02-24.wav\n", "03-02-02-01-01-01-24.wav\n", "03-02-01-01-02-02-24.wav\n", "03-02-02-01-02-01-24.wav\n", "03-02-02-01-02-02-24.wav\n", "03-02-02-02-01-01-24.wav\n", "03-02-02-01-01-02-24.wav\n", "03-02-02-02-02-01-24.wav\n", "03-02-02-02-01-02-24.wav\n", "03-02-03-01-02-01-24.wav\n", "03-02-03-01-01-01-24.wav\n", "03-02-02-02-02-02-24.wav\n", "03-02-03-02-01-01-24.wav\n", "03-02-03-01-01-02-24.wav\n", "03-02-03-01-02-02-24.wav\n", "03-02-03-02-01-02-24.wav\n", "03-02-03-02-02-01-24.wav\n", "03-02-04-01-02-02-24.wav\n", "03-02-03-02-02-02-24.wav\n", "03-02-04-02-01-01-24.wav\n", "03-02-04-01-02-01-24.wav\n", "03-02-04-01-01-01-24.wav\n", "03-02-04-02-01-02-24.wav\n", "03-02-04-01-01-02-24.wav\n", "03-02-04-02-02-01-24.wav\n", "03-02-04-02-02-02-24.wav\n", "03-02-05-02-01-02-24.wav\n", "03-02-05-01-01-01-24.wav\n", "03-02-05-02-02-01-24.wav\n", "03-02-05-01-01-02-24.wav\n", "03-02-05-02-02-02-24.wav\n", "03-02-05-01-02-01-24.wav\n", "03-02-05-01-02-02-24.wav\n", "03-02-06-01-01-01-24.wav\n", "03-02-05-02-01-01-24.wav\n", "03-02-06-01-02-01-24.wav\n", "03-02-06-01-01-02-24.wav\n", "03-02-06-02-01-02-24.wav\n", "03-02-06-01-02-02-24.wav\n", "03-02-06-02-02-01-24.wav\n", "03-02-06-02-01-01-24.wav\n", "03-02-06-02-02-02-24.wav\n", "03-01-01-01-01-01-18.wav\n", "03-01-01-01-01-02-18.wav\n", "03-01-01-01-02-01-18.wav\n", "03-01-02-01-01-01-18.wav\n", "03-01-01-01-02-02-18.wav\n", "03-01-02-01-01-02-18.wav\n", "03-01-02-01-02-01-18.wav\n", "03-01-02-01-02-02-18.wav\n", "03-01-02-02-02-01-18.wav\n", "03-01-02-02-01-01-18.wav\n", "03-01-02-02-02-02-18.wav\n", "03-01-02-02-01-02-18.wav\n", "03-01-03-01-01-02-18.wav\n", "03-01-03-01-01-01-18.wav\n", "03-01-03-01-02-01-18.wav\n", "03-01-03-01-02-02-18.wav\n", "03-01-04-01-01-01-18.wav\n", "03-01-03-02-01-01-18.wav\n", "03-01-04-01-01-02-18.wav\n", "03-01-03-02-01-02-18.wav\n", "03-01-04-01-02-01-18.wav\n", "03-01-03-02-02-01-18.wav\n", "03-01-04-01-02-02-18.wav\n", "03-01-03-02-02-02-18.wav\n", "03-01-04-02-01-01-18.wav\n", "03-01-05-01-01-01-18.wav\n", "03-01-04-02-01-02-18.wav\n", "03-01-05-01-02-01-18.wav\n", "03-01-04-02-02-01-18.wav\n", "03-01-05-01-01-02-18.wav\n", "03-01-04-02-02-02-18.wav\n", "03-01-05-01-02-02-18.wav\n", "03-01-05-02-01-01-18.wav\n", "03-01-05-02-02-01-18.wav\n", "03-01-05-02-01-02-18.wav\n", "03-01-06-01-01-01-18.wav\n", "03-01-06-01-01-02-18.wav\n", "03-01-05-02-02-02-18.wav\n", "03-01-06-01-02-01-18.wav\n", "03-01-06-01-02-02-18.wav\n", "03-01-07-01-01-02-18.wav\n", "03-01-06-02-02-01-18.wav\n", "03-01-06-02-01-01-18.wav\n", "03-01-06-02-01-02-18.wav\n", "03-01-07-01-02-01-18.wav\n", "03-01-07-01-01-01-18.wav\n", "03-01-06-02-02-02-18.wav\n", "03-01-07-01-02-02-18.wav\n", "03-01-07-02-01-01-18.wav\n", "03-01-08-02-01-02-18.wav\n", "03-01-07-02-01-02-18.wav\n", "03-01-08-01-02-01-18.wav\n", "03-01-07-02-02-01-18.wav\n", "03-01-08-01-02-02-18.wav\n", "03-01-07-02-02-02-18.wav\n", "03-01-08-02-01-01-18.wav\n", "03-01-08-01-01-01-18.wav\n", "03-01-08-01-01-02-18.wav\n", "03-01-08-02-02-01-18.wav\n", "03-01-08-02-02-02-18.wav\n", "03-01-01-01-02-01-10.wav\n", "03-01-01-01-01-01-10.wav\n", "03-01-01-01-01-02-10.wav\n", "03-01-01-01-02-02-10.wav\n", "03-01-02-01-01-01-10.wav\n", "03-01-02-01-01-02-10.wav\n", "03-01-02-01-02-01-10.wav\n", "03-01-02-01-02-02-10.wav\n", "03-01-03-01-01-01-10.wav\n", "03-01-02-02-01-01-10.wav\n", "03-01-03-01-01-02-10.wav\n", "03-01-02-02-01-02-10.wav\n", "03-01-03-01-02-01-10.wav\n", "03-01-02-02-02-01-10.wav\n", "03-01-02-02-02-02-10.wav\n", "03-01-03-01-02-02-10.wav\n", "03-01-03-02-01-01-10.wav\n", "03-01-04-01-02-01-10.wav\n", "03-01-03-02-01-02-10.wav\n", "03-01-04-01-02-02-10.wav\n", "03-01-04-01-01-01-10.wav\n", "03-01-04-02-01-01-10.wav\n", "03-01-03-02-02-01-10.wav\n", "03-01-04-02-01-02-10.wav\n", "03-01-03-02-02-02-10.wav\n", "03-01-04-01-01-02-10.wav\n", "03-01-04-02-02-01-10.wav\n", "03-01-04-02-02-02-10.wav\n", "03-01-05-01-02-01-10.wav\n", "03-01-05-01-01-01-10.wav\n", "03-01-05-01-02-02-10.wav\n", "03-01-05-01-01-02-10.wav\n", "03-01-05-02-02-01-10.wav\n", "03-01-05-02-01-01-10.wav\n", "03-01-05-02-02-02-10.wav\n", "03-01-05-02-01-02-10.wav\n", "03-01-06-01-01-01-10.wav\n", "03-01-06-01-02-02-10.wav\n", "03-01-06-01-01-02-10.wav\n", "03-01-06-01-02-01-10.wav\n", "03-01-06-02-01-01-10.wav\n", "03-01-06-02-01-02-10.wav\n", "03-01-06-02-02-01-10.wav\n", "03-01-07-01-02-01-10.wav\n", "03-01-07-01-01-02-10.wav\n", "03-01-07-01-01-01-10.wav\n", "03-01-06-02-02-02-10.wav\n", "03-01-07-02-01-01-10.wav\n", "03-01-07-01-02-02-10.wav\n", "03-01-07-02-01-02-10.wav\n", "03-01-07-02-02-01-10.wav\n", "03-01-08-01-01-01-10.wav\n", "03-01-08-01-01-02-10.wav\n", "03-01-07-02-02-02-10.wav\n", "03-01-08-01-02-01-10.wav\n", "03-01-08-01-02-02-10.wav\n", "03-01-08-02-01-01-10.wav\n", "03-02-01-01-01-01-10.wav\n", "03-01-08-02-02-02-10.wav\n", "03-01-08-02-01-02-10.wav\n", "03-01-08-02-02-01-10.wav\n", "03-02-01-01-01-02-10.wav\n", "03-02-01-01-02-01-10.wav\n", "03-02-01-01-02-02-10.wav\n", "03-02-02-01-01-01-10.wav\n", "03-02-02-01-01-02-10.wav\n", "03-02-02-01-02-01-10.wav\n", "03-02-02-02-02-01-10.wav\n", "03-02-02-02-01-01-10.wav\n", "03-02-02-01-02-02-10.wav\n", "03-02-03-01-01-01-10.wav\n", "03-02-02-02-02-02-10.wav\n", "03-02-02-02-01-02-10.wav\n", "03-02-03-01-01-02-10.wav\n", "03-02-03-01-02-01-10.wav\n", "03-02-03-01-02-02-10.wav\n", "03-02-04-01-01-01-10.wav\n", "03-02-03-02-01-01-10.wav\n", "03-02-04-01-02-01-10.wav\n", "03-02-04-01-01-02-10.wav\n", "03-02-03-02-02-01-10.wav\n", "03-02-03-02-01-02-10.wav\n", "03-02-04-01-02-02-10.wav\n", "03-02-03-02-02-02-10.wav\n", "03-02-04-02-01-01-10.wav\n", "03-02-05-01-01-01-10.wav\n", "03-02-04-02-01-02-10.wav\n", "03-02-05-01-01-02-10.wav\n", "03-02-04-02-02-02-10.wav\n", "03-02-04-02-02-01-10.wav\n", "03-02-05-02-01-01-10.wav\n", "03-02-05-01-02-02-10.wav\n", "03-02-05-01-02-01-10.wav\n", "03-02-05-02-01-02-10.wav\n", "03-02-05-02-02-01-10.wav\n", "03-02-05-02-02-02-10.wav\n", "03-02-06-01-01-01-10.wav\n", "03-02-06-01-02-01-10.wav\n", "03-02-06-01-01-02-10.wav\n", "03-02-06-02-01-01-10.wav\n", "03-02-06-01-02-02-10.wav\n", "03-02-06-02-01-02-10.wav\n", "03-02-06-02-02-02-10.wav\n", "03-02-06-02-02-01-10.wav\n", "03-01-02-01-01-01-07.wav\n", "03-01-01-01-01-02-07.wav\n", "03-01-01-01-01-01-07.wav\n", "03-01-02-01-02-01-07.wav\n", "03-01-02-01-01-02-07.wav\n", "03-01-01-01-02-01-07.wav\n", "03-01-01-01-02-02-07.wav\n", "03-01-02-01-02-02-07.wav\n", "03-01-02-02-01-01-07.wav\n", "03-01-03-01-02-01-07.wav\n", "03-01-02-02-01-02-07.wav\n", "03-01-03-01-02-02-07.wav\n", "03-01-03-02-01-01-07.wav\n", "03-01-02-02-02-02-07.wav\n", "03-01-03-01-01-02-07.wav\n", "03-01-03-01-01-01-07.wav\n", "03-01-03-02-01-02-07.wav\n", "03-01-02-02-02-01-07.wav\n", "03-01-03-02-02-01-07.wav\n", "03-01-03-02-02-02-07.wav\n", "03-01-04-01-01-01-07.wav\n", "03-01-04-01-01-02-07.wav\n", "03-01-04-01-02-01-07.wav\n", "03-01-04-01-02-02-07.wav\n", "03-01-04-02-01-01-07.wav\n", "03-01-04-02-02-01-07.wav\n", "03-01-05-01-01-01-07.wav\n", "03-01-04-02-01-02-07.wav\n", "03-01-05-01-01-02-07.wav\n", "03-01-04-02-02-02-07.wav\n", "03-01-05-01-02-01-07.wav\n", "03-01-05-01-02-02-07.wav\n", "03-01-05-02-01-01-07.wav\n", "03-01-05-02-01-02-07.wav\n", "03-01-06-01-02-01-07.wav\n", "03-01-05-02-02-01-07.wav\n", "03-01-06-01-02-02-07.wav\n", "03-01-06-01-01-02-07.wav\n", "03-01-06-01-01-01-07.wav\n", "03-01-06-02-01-01-07.wav\n", "03-01-05-02-02-02-07.wav\n", "03-01-06-02-01-02-07.wav\n", "03-01-06-02-02-01-07.wav\n", "03-01-06-02-02-02-07.wav\n", "03-01-07-01-02-01-07.wav\n", "03-01-07-01-01-01-07.wav\n", "03-01-07-02-01-01-07.wav\n", "03-01-07-01-01-02-07.wav\n", "03-01-07-01-02-02-07.wav\n", "03-01-08-01-01-01-07.wav\n", "03-01-08-01-02-01-07.wav\n", "03-01-08-01-01-02-07.wav\n", "03-01-08-02-01-01-07.wav\n", "03-01-08-01-02-02-07.wav\n", "03-01-08-02-01-02-07.wav\n", "03-01-08-02-02-01-07.wav\n", "03-01-08-02-02-02-07.wav\n", "03-02-01-01-01-01-07.wav\n", "03-02-01-01-01-02-07.wav\n", "03-02-01-01-02-02-07.wav\n", "03-02-01-01-02-01-07.wav\n", "03-01-07-02-02-02-07.wav\n", "03-01-07-02-02-01-07.wav\n", "03-01-07-02-01-02-07.wav\n", "03-02-02-01-01-01-07.wav\n", "03-02-02-01-01-02-07.wav\n", "03-02-03-01-01-01-07.wav\n", "03-02-02-01-02-01-07.wav\n", "03-02-02-02-02-01-07.wav\n", "03-02-02-01-02-02-07.wav\n", "03-02-02-02-02-02-07.wav\n", "03-02-02-02-01-02-07.wav\n", "03-02-03-01-01-02-07.wav\n", "03-02-02-02-01-01-07.wav\n", "03-02-03-01-02-01-07.wav\n", "03-02-03-01-02-02-07.wav\n", "03-02-03-02-02-01-07.wav\n", "03-02-03-02-01-01-07.wav\n", "03-02-03-02-02-02-07.wav\n", "03-02-03-02-01-02-07.wav\n", "03-02-04-01-01-01-07.wav\n", "03-02-04-01-02-02-07.wav\n", "03-02-04-02-01-01-07.wav\n", "03-02-04-01-01-02-07.wav\n", "03-02-04-02-01-02-07.wav\n", "03-02-04-01-02-01-07.wav\n", "03-02-04-02-02-01-07.wav\n", "03-02-04-02-02-02-07.wav\n", "03-02-05-01-01-01-07.wav\n", "03-02-05-01-02-01-07.wav\n", "03-02-05-01-01-02-07.wav\n", "03-02-06-01-01-01-07.wav\n", "03-02-05-01-02-02-07.wav\n", "03-02-05-02-02-01-07.wav\n", "03-02-05-02-01-01-07.wav\n", "03-02-05-02-02-02-07.wav\n", "03-02-06-01-01-02-07.wav\n", "03-02-06-02-01-01-07.wav\n", "03-02-05-02-01-02-07.wav\n", "03-02-06-02-01-02-07.wav\n", "03-02-06-01-02-01-07.wav\n", "03-02-06-01-02-02-07.wav\n", "03-02-06-02-02-01-07.wav\n", "03-02-06-02-02-02-07.wav\n", "03-01-01-01-01-01-09.wav\n", "03-01-01-01-01-02-09.wav\n", "03-01-01-01-02-01-09.wav\n", "03-01-01-01-02-02-09.wav\n", "03-01-02-01-01-01-09.wav\n", "03-01-02-01-01-02-09.wav\n", "03-01-02-01-02-01-09.wav\n", "03-01-02-02-02-01-09.wav\n", "03-01-02-01-02-02-09.wav\n", "03-01-02-02-01-02-09.wav\n", "03-01-02-02-01-01-09.wav\n", "03-01-03-01-01-02-09.wav\n", "03-01-03-01-01-01-09.wav\n", "03-01-02-02-02-02-09.wav\n", "03-01-03-01-02-01-09.wav\n", "03-01-03-02-01-01-09.wav\n", "03-01-03-01-02-02-09.wav\n", "03-01-03-02-02-01-09.wav\n", "03-01-04-01-01-01-09.wav\n", "03-01-03-02-01-02-09.wav\n", "03-01-03-02-02-02-09.wav\n", "03-01-04-01-02-01-09.wav\n", "03-01-04-01-01-02-09.wav\n", "03-01-04-01-02-02-09.wav\n", "03-01-04-02-01-01-09.wav\n", "03-01-04-02-01-02-09.wav\n", "03-01-05-01-01-02-09.wav\n", "03-01-05-01-02-01-09.wav\n", "03-01-05-01-02-02-09.wav\n", "03-01-05-01-01-01-09.wav\n", "03-01-04-02-02-01-09.wav\n", "03-01-04-02-02-02-09.wav\n", "03-01-05-02-01-01-09.wav\n", "03-01-05-02-01-02-09.wav\n", "03-01-05-02-02-02-09.wav\n", "03-01-06-01-02-01-09.wav\n", "03-01-06-02-01-01-09.wav\n", "03-01-05-02-02-01-09.wav\n", "03-01-06-02-01-02-09.wav\n", "03-01-06-01-01-01-09.wav\n", "03-01-06-01-01-02-09.wav\n", "03-01-06-02-02-01-09.wav\n", "03-01-06-01-02-02-09.wav\n", "03-01-06-02-02-02-09.wav\n", "03-01-07-01-01-01-09.wav\n", "03-01-07-02-02-01-09.wav\n", "03-01-07-01-01-02-09.wav\n", "03-01-07-02-02-02-09.wav\n", "03-01-07-01-02-01-09.wav\n", "03-01-08-01-01-01-09.wav\n", "03-01-07-01-02-02-09.wav\n", "03-01-08-01-01-02-09.wav\n", "03-01-07-02-01-02-09.wav\n", "03-01-07-02-01-01-09.wav\n", "03-01-08-01-02-01-09.wav\n", "03-01-08-01-02-02-09.wav\n", "03-01-08-02-02-01-09.wav\n", "03-01-08-02-01-02-09.wav\n", "03-01-08-02-01-01-09.wav\n", "03-01-08-02-02-02-09.wav\n", "03-02-01-01-01-01-09.wav\n", "03-02-01-01-02-01-09.wav\n", "03-02-01-01-01-02-09.wav\n", "03-02-02-01-01-01-09.wav\n", "03-02-01-01-02-02-09.wav\n", "03-02-02-01-01-02-09.wav\n", "03-02-02-01-02-01-09.wav\n", "03-02-02-01-02-02-09.wav\n", "03-02-02-02-01-01-09.wav\n", "03-02-02-02-01-02-09.wav\n", "03-02-02-02-02-01-09.wav\n", "03-02-03-01-02-01-09.wav\n", "03-02-03-01-01-01-09.wav\n", "03-02-03-01-02-02-09.wav\n", "03-02-03-01-01-02-09.wav\n", "03-02-03-02-01-01-09.wav\n", "03-02-02-02-02-02-09.wav\n", "03-02-03-02-01-02-09.wav\n", "03-02-03-02-02-01-09.wav\n", "03-02-04-02-02-02-09.wav\n", "03-02-03-02-02-02-09.wav\n", "03-02-04-02-01-01-09.wav\n", "03-02-04-01-01-01-09.wav\n", "03-02-04-02-02-01-09.wav\n", "03-02-04-02-01-02-09.wav\n", "03-02-04-01-02-02-09.wav\n", "03-02-04-01-02-01-09.wav\n", "03-02-04-01-01-02-09.wav\n", "03-02-05-01-01-01-09.wav\n", "03-02-05-01-02-01-09.wav\n", "03-02-05-01-01-02-09.wav\n", "03-02-05-01-02-02-09.wav\n", "03-02-05-02-01-01-09.wav\n", "03-02-05-02-02-01-09.wav\n", "03-02-05-02-01-02-09.wav\n", "03-02-06-01-01-01-09.wav\n", "03-02-06-01-02-01-09.wav\n", "03-02-06-01-01-02-09.wav\n", "03-02-05-02-02-02-09.wav\n", "03-02-06-01-02-02-09.wav\n", "03-02-06-02-01-02-09.wav\n", "03-02-06-02-01-01-09.wav\n", "03-02-06-02-02-01-09.wav\n", "03-02-06-02-02-02-09.wav\n", "03-01-01-01-02-02-08.wav\n", "03-01-02-01-01-01-08.wav\n", "03-01-01-01-01-01-08.wav\n", "03-01-01-01-01-02-08.wav\n", "03-01-01-01-02-01-08.wav\n", "03-01-02-01-02-01-08.wav\n", "03-01-02-01-01-02-08.wav\n", "03-01-02-01-02-02-08.wav\n", "03-01-02-02-01-01-08.wav\n", "03-01-02-02-01-02-08.wav\n", "03-01-03-01-02-01-08.wav\n", "03-01-02-02-02-01-08.wav\n", "03-01-03-01-02-02-08.wav\n", "03-01-03-01-01-01-08.wav\n", "03-01-02-02-02-02-08.wav\n", "03-01-03-02-01-01-08.wav\n", "03-01-03-01-01-02-08.wav\n", "03-01-03-02-01-02-08.wav\n", "03-01-03-02-02-01-08.wav\n", "03-01-03-02-02-02-08.wav\n", "03-01-04-01-01-01-08.wav\n", "03-01-04-01-02-02-08.wav\n", "03-01-04-01-02-01-08.wav\n", "03-01-04-01-01-02-08.wav\n", "03-01-04-02-01-01-08.wav\n", "03-01-04-02-01-02-08.wav\n", "03-01-04-02-02-01-08.wav\n", "03-01-04-02-02-02-08.wav\n", "03-01-05-01-01-01-08.wav\n", "03-01-05-01-02-01-08.wav\n", "03-01-05-01-01-02-08.wav\n", "03-01-05-02-02-01-08.wav\n", "03-01-05-02-01-01-08.wav\n", "03-01-05-01-02-02-08.wav\n", "03-01-05-02-02-02-08.wav\n", "03-01-05-02-01-02-08.wav\n", "03-01-06-01-01-02-08.wav\n", "03-01-06-01-01-01-08.wav\n", "03-01-06-01-02-01-08.wav\n", "03-01-06-01-02-02-08.wav\n", "03-01-07-01-01-01-08.wav\n", "03-01-06-02-01-01-08.wav\n", "03-01-07-01-01-02-08.wav\n", "03-01-06-02-01-02-08.wav\n", "03-01-07-01-02-01-08.wav\n", "03-01-07-01-02-02-08.wav\n", "03-01-06-02-02-01-08.wav\n", "03-01-06-02-02-02-08.wav\n", "03-01-07-02-01-01-08.wav\n", "03-01-08-01-01-01-08.wav\n", "03-01-07-02-01-02-08.wav\n", "03-01-08-01-01-02-08.wav\n", "03-01-07-02-02-01-08.wav\n", "03-01-07-02-02-02-08.wav\n", "03-01-08-02-01-01-08.wav\n", "03-01-08-01-02-02-08.wav\n", "03-01-08-01-02-01-08.wav\n", "03-01-08-02-01-02-08.wav\n", "03-01-08-02-02-01-08.wav\n", "03-01-08-02-02-02-08.wav\n", "03-02-01-01-01-01-08.wav\n", "03-02-01-01-02-01-08.wav\n", "03-02-01-01-02-02-08.wav\n", "03-02-01-01-01-02-08.wav\n", "03-02-02-01-01-01-08.wav\n", "03-02-02-01-02-01-08.wav\n", "03-02-02-01-01-02-08.wav\n", "03-02-02-01-02-02-08.wav\n", "03-02-02-02-01-01-08.wav\n", "03-02-02-02-01-02-08.wav\n", "03-02-02-02-02-01-08.wav\n", "03-02-03-01-01-01-08.wav\n", "03-02-02-02-02-02-08.wav\n", "03-02-03-01-01-02-08.wav\n", "03-02-03-01-02-02-08.wav\n", "03-02-03-01-02-01-08.wav\n", "03-02-03-02-02-01-08.wav\n", "03-02-03-02-01-01-08.wav\n", "03-02-03-02-01-02-08.wav\n", "03-02-04-01-01-01-08.wav\n", "03-02-03-02-02-02-08.wav\n", "03-02-04-01-01-02-08.wav\n", "03-02-04-01-02-01-08.wav\n", "03-02-04-01-02-02-08.wav\n", "03-02-04-02-01-01-08.wav\n", "03-02-05-01-02-02-08.wav\n", "03-02-04-02-01-02-08.wav\n", "03-02-04-02-02-01-08.wav\n", "03-02-05-01-01-02-08.wav\n", "03-02-05-01-01-01-08.wav\n", "03-02-05-01-02-01-08.wav\n", "03-02-04-02-02-02-08.wav\n", "03-02-06-01-01-01-08.wav\n", "03-02-05-02-01-02-08.wav\n", "03-02-06-01-01-02-08.wav\n", "03-02-06-01-02-01-08.wav\n", "03-02-05-02-02-01-08.wav\n", "03-02-05-02-02-02-08.wav\n", "03-02-06-02-01-01-08.wav\n", "03-02-06-01-02-02-08.wav\n", "03-02-06-02-01-02-08.wav\n", "03-02-05-02-01-01-08.wav\n", "03-02-06-02-02-01-08.wav\n", "03-02-06-02-02-02-08.wav\n", "03-01-01-01-01-01-05.wav\n", "03-01-02-01-01-02-05.wav\n", "03-01-01-01-01-02-05.wav\n", "03-01-02-01-02-01-05.wav\n", "03-01-01-01-02-01-05.wav\n", "03-01-02-02-01-01-05.wav\n", "03-01-01-01-02-02-05.wav\n", "03-01-02-01-02-02-05.wav\n", "03-01-02-01-01-01-05.wav\n", "03-01-02-02-01-02-05.wav\n", "03-01-03-01-01-02-05.wav\n", "03-01-02-02-02-01-05.wav\n", "03-01-03-01-02-02-05.wav\n", "03-01-02-02-02-02-05.wav\n", "03-01-03-01-01-01-05.wav\n", "03-01-03-02-01-01-05.wav\n", "03-01-03-01-02-01-05.wav\n", "03-01-03-02-01-02-05.wav\n", "03-01-03-02-02-01-05.wav\n", "03-01-03-02-02-02-05.wav\n", "03-01-04-01-01-01-05.wav\n", "03-01-04-01-01-02-05.wav\n", "03-01-04-01-02-02-05.wav\n", "03-01-04-01-02-01-05.wav\n", "03-01-04-02-01-01-05.wav\n", "03-01-04-02-01-02-05.wav\n", "03-01-05-01-01-02-05.wav\n", "03-01-04-02-02-01-05.wav\n", "03-01-05-01-02-01-05.wav\n", "03-01-04-02-02-02-05.wav\n", "03-01-05-02-01-01-05.wav\n", "03-01-05-01-01-01-05.wav\n", "03-01-05-01-02-02-05.wav\n", "03-01-05-02-01-02-05.wav\n", "03-01-06-01-02-02-05.wav\n", "03-01-05-02-02-01-05.wav\n", "03-01-06-02-01-01-05.wav\n", "03-01-05-02-02-02-05.wav\n", "03-01-06-02-01-02-05.wav\n", "03-01-06-01-01-02-05.wav\n", "03-01-06-02-02-01-05.wav\n", "03-01-06-01-02-01-05.wav\n", "03-01-06-01-01-01-05.wav\n", "03-01-06-02-02-02-05.wav\n", "03-01-07-01-01-01-05.wav\n", "03-01-07-01-01-02-05.wav\n", "03-01-07-01-02-02-05.wav\n", "03-01-07-01-02-01-05.wav\n", "03-01-07-02-02-02-05.wav\n", "03-01-07-02-02-01-05.wav\n", "03-01-07-02-01-01-05.wav\n", "03-01-08-01-01-01-05.wav\n", "03-01-07-02-01-02-05.wav\n", "03-01-08-01-01-02-05.wav\n", "03-01-08-02-01-02-05.wav\n", "03-01-08-01-02-01-05.wav\n", "03-01-08-01-02-02-05.wav\n", "03-01-08-02-02-01-05.wav\n", "03-01-08-02-01-01-05.wav\n", "03-01-08-02-02-02-05.wav\n", "03-02-01-01-01-01-05.wav\n", "03-02-01-01-01-02-05.wav\n", "03-02-01-01-02-01-05.wav\n", "03-02-01-01-02-02-05.wav\n", "03-02-02-01-01-01-05.wav\n", "03-02-02-01-01-02-05.wav\n", "03-02-02-01-02-01-05.wav\n", "03-02-02-01-02-02-05.wav\n", "03-02-02-02-01-01-05.wav\n", "03-02-03-01-01-02-05.wav\n", "03-02-02-02-01-02-05.wav\n", "03-02-02-02-02-01-05.wav\n", "03-02-03-01-02-01-05.wav\n", "03-02-02-02-02-02-05.wav\n", "03-02-03-01-02-02-05.wav\n", "03-02-03-01-01-01-05.wav\n", "03-02-03-02-01-01-05.wav\n", "03-02-03-02-01-02-05.wav\n", "03-02-04-01-02-02-05.wav\n", "03-02-03-02-02-01-05.wav\n", "03-02-04-02-01-01-05.wav\n", "03-02-03-02-02-02-05.wav\n", "03-02-04-02-01-02-05.wav\n", "03-02-04-01-01-01-05.wav\n", "03-02-04-02-02-01-05.wav\n", "03-02-04-01-01-02-05.wav\n", "03-02-04-01-02-01-05.wav\n", "03-02-04-02-02-02-05.wav\n", "03-02-05-01-01-01-05.wav\n", "03-02-05-01-02-02-05.wav\n", "03-02-05-01-01-02-05.wav\n", "03-02-05-02-01-01-05.wav\n", "03-02-05-01-02-01-05.wav\n", "03-02-05-02-01-02-05.wav\n", "03-02-05-02-02-02-05.wav\n", "03-02-05-02-02-01-05.wav\n", "03-02-06-01-01-01-05.wav\n", "03-02-06-01-02-02-05.wav\n", "03-02-06-01-01-02-05.wav\n", "03-02-06-02-01-01-05.wav\n", "03-02-06-01-02-01-05.wav\n", "03-02-06-02-01-02-05.wav\n", "03-02-06-02-02-01-05.wav\n", "03-02-06-02-02-02-05.wav\n", "03-01-01-01-02-01-14.wav\n", "03-01-01-01-01-02-14.wav\n", "03-01-01-01-01-01-14.wav\n", "03-01-01-01-02-02-14.wav\n", "03-01-02-01-01-01-14.wav\n", "03-01-02-01-02-01-14.wav\n", "03-01-02-01-01-02-14.wav\n", "03-01-02-01-02-02-14.wav\n", "03-01-02-02-01-01-14.wav\n", "03-01-02-02-01-02-14.wav\n", "03-01-02-02-02-01-14.wav\n", "03-01-03-01-02-01-14.wav\n", "03-01-02-02-02-02-14.wav\n", "03-01-03-01-01-02-14.wav\n", "03-01-03-01-01-01-14.wav\n", "03-01-03-01-02-02-14.wav\n", "03-01-03-02-01-02-14.wav\n", "03-01-03-02-01-01-14.wav\n", "03-01-03-02-02-01-14.wav\n", "03-01-03-02-02-02-14.wav\n", "03-01-04-02-01-01-14.wav\n", "03-01-04-01-01-02-14.wav\n", "03-01-04-01-01-01-14.wav\n", "03-01-04-02-01-02-14.wav\n", "03-01-04-01-02-01-14.wav\n", "03-01-04-01-02-02-14.wav\n", "03-01-04-02-02-02-14.wav\n", "03-01-04-02-02-01-14.wav\n", "03-01-05-01-01-01-14.wav\n", "03-01-05-01-01-02-14.wav\n", "03-01-05-02-02-01-14.wav\n", "03-01-05-01-02-01-14.wav\n", "03-01-05-01-02-02-14.wav\n", "03-01-06-01-01-01-14.wav\n", "03-01-06-01-01-02-14.wav\n", "03-01-05-02-01-01-14.wav\n", "03-01-05-02-02-02-14.wav\n", "03-01-05-02-01-02-14.wav\n", "03-01-06-01-02-01-14.wav\n", "03-01-06-01-02-02-14.wav\n", "03-01-06-02-02-02-14.wav\n", "03-01-06-02-01-01-14.wav\n", "03-01-06-02-02-01-14.wav\n", "03-01-06-02-01-02-14.wav\n", "03-01-07-01-02-01-14.wav\n", "03-01-07-01-01-02-14.wav\n", "03-01-07-01-01-01-14.wav\n", "03-01-07-01-02-02-14.wav\n", "03-01-07-02-01-01-14.wav\n", "03-01-07-02-02-01-14.wav\n", "03-01-07-02-01-02-14.wav\n", "03-01-08-01-01-01-14.wav\n", "03-01-07-02-02-02-14.wav\n", "03-01-08-01-01-02-14.wav\n", "03-01-08-01-02-01-14.wav\n", "03-01-08-02-02-01-14.wav\n", "03-01-08-02-01-01-14.wav\n", "03-01-08-02-01-02-14.wav\n", "03-01-08-01-02-02-14.wav\n", "03-02-01-01-01-01-14.wav\n", "03-01-08-02-02-02-14.wav\n", "03-02-01-01-01-02-14.wav\n", "03-02-01-01-02-01-14.wav\n", "03-02-02-01-01-01-14.wav\n", "03-02-01-01-02-02-14.wav\n", "03-02-02-02-01-01-14.wav\n", "03-02-02-01-01-02-14.wav\n", "03-02-02-01-02-02-14.wav\n", "03-02-02-02-01-02-14.wav\n", "03-02-02-01-02-01-14.wav\n", "03-02-02-02-02-02-14.wav\n", "03-02-02-02-02-01-14.wav\n", "03-02-03-01-01-01-14.wav\n", "03-02-03-02-02-01-14.wav\n", "03-02-03-01-01-02-14.wav\n", "03-02-03-02-02-02-14.wav\n", "03-02-04-01-01-01-14.wav\n", "03-02-03-01-02-01-14.wav\n", "03-02-04-01-01-02-14.wav\n", "03-02-03-01-02-02-14.wav\n", "03-02-03-02-01-02-14.wav\n", "03-02-03-02-01-01-14.wav\n", "03-02-04-01-02-01-14.wav\n", "03-02-04-01-02-02-14.wav\n", "03-02-04-02-01-01-14.wav\n", "03-02-04-02-02-01-14.wav\n", "03-02-04-02-01-02-14.wav\n", "03-02-04-02-02-02-14.wav\n", "03-02-05-01-01-01-14.wav\n", "03-02-05-01-01-02-14.wav\n", "03-02-05-02-01-01-14.wav\n", "03-02-05-01-02-01-14.wav\n", "03-02-05-02-01-02-14.wav\n", "03-02-05-01-02-02-14.wav\n", "03-02-05-02-02-01-14.wav\n", "03-02-05-02-02-02-14.wav\n", "03-02-06-01-01-02-14.wav\n", "03-02-06-01-02-01-14.wav\n", "03-02-06-02-02-01-14.wav\n", "03-02-06-01-01-01-14.wav\n", "03-02-06-02-02-02-14.wav\n", "03-02-06-02-01-02-14.wav\n", "03-02-06-01-02-02-14.wav\n", "03-02-06-02-01-01-14.wav\n", "03-01-01-01-01-01-11.wav\n", "03-01-01-01-01-02-11.wav\n", "03-01-01-01-02-02-11.wav\n", "03-01-01-01-02-01-11.wav\n", "03-01-02-01-01-01-11.wav\n", "03-01-02-01-02-01-11.wav\n", "03-01-02-01-01-02-11.wav\n", "03-01-02-02-01-01-11.wav\n", "03-01-02-01-02-02-11.wav\n", "03-01-02-02-01-02-11.wav\n", "03-01-02-02-02-01-11.wav\n", "03-01-02-02-02-02-11.wav\n", "03-01-03-01-01-01-11.wav\n", "03-01-03-02-01-01-11.wav\n", "03-01-03-01-01-02-11.wav\n", "03-01-03-01-02-01-11.wav\n", "03-01-03-01-02-02-11.wav\n", "03-01-04-01-01-01-11.wav\n", "03-01-04-01-01-02-11.wav\n", "03-01-04-02-01-02-11.wav\n", "03-01-04-02-01-01-11.wav\n", "03-01-04-01-02-02-11.wav\n", "03-01-04-01-02-01-11.wav\n", "03-01-03-02-01-02-11.wav\n", "03-01-03-02-02-01-11.wav\n", "03-01-03-02-02-02-11.wav\n", "03-01-04-02-02-01-11.wav\n", "03-01-05-01-01-01-11.wav\n", "03-01-04-02-02-02-11.wav\n", "03-01-05-01-02-01-11.wav\n", "03-01-05-01-01-02-11.wav\n", "03-01-05-02-02-01-11.wav\n", "03-01-05-01-02-02-11.wav\n", "03-01-05-02-01-01-11.wav\n", "03-01-05-02-01-02-11.wav\n", "03-01-05-02-02-02-11.wav\n", "03-01-06-01-01-01-11.wav\n", "03-01-06-01-01-02-11.wav\n", "03-01-06-01-02-01-11.wav\n", "03-01-06-02-02-01-11.wav\n", "03-01-06-01-02-02-11.wav\n", "03-01-06-02-02-02-11.wav\n", "03-01-06-02-01-01-11.wav\n", "03-01-06-02-01-02-11.wav\n", "03-01-07-01-01-02-11.wav\n", "03-01-07-01-01-01-11.wav\n", "03-01-07-01-02-01-11.wav\n", "03-01-08-01-01-01-11.wav\n", "03-01-07-01-02-02-11.wav\n", "03-01-07-02-01-01-11.wav\n", "03-01-08-01-02-01-11.wav\n", "03-01-08-01-01-02-11.wav\n", "03-01-07-02-01-02-11.wav\n", "03-01-07-02-02-02-11.wav\n", "03-01-08-01-02-02-11.wav\n", "03-01-07-02-02-01-11.wav\n", "03-01-08-02-01-01-11.wav\n", "03-02-01-01-01-01-11.wav\n", "03-01-08-02-02-01-11.wav\n", "03-01-08-02-01-02-11.wav\n", "03-02-01-01-01-02-11.wav\n", "03-02-01-01-02-01-11.wav\n", "03-01-08-02-02-02-11.wav\n", "03-02-01-01-02-02-11.wav\n", "03-02-02-01-01-01-11.wav\n", "03-02-02-01-01-02-11.wav\n", "03-02-02-01-02-01-11.wav\n", "03-02-02-01-02-02-11.wav\n", "03-02-03-01-01-01-11.wav\n", "03-02-02-02-01-01-11.wav\n", "03-02-03-01-01-02-11.wav\n", "03-02-02-02-01-02-11.wav\n", "03-02-02-02-02-01-11.wav\n", "03-02-02-02-02-02-11.wav\n", "03-02-03-01-02-01-11.wav\n", "03-02-03-01-02-02-11.wav\n", "03-02-03-02-01-01-11.wav\n", "03-02-03-02-01-02-11.wav\n", "03-02-03-02-02-01-11.wav\n", "03-02-03-02-02-02-11.wav\n", "03-02-04-01-01-01-11.wav\n", "03-02-04-01-01-02-11.wav\n", "03-02-04-01-02-01-11.wav\n", "03-02-05-01-01-01-11.wav\n", "03-02-05-01-01-02-11.wav\n", "03-02-04-01-02-02-11.wav\n", "03-02-04-02-01-02-11.wav\n", "03-02-04-02-01-01-11.wav\n", "03-02-04-02-02-02-11.wav\n", "03-02-04-02-02-01-11.wav\n", "03-02-05-01-02-01-11.wav\n", "03-02-06-01-01-01-11.wav\n", "03-02-05-01-02-02-11.wav\n", "03-02-05-02-01-01-11.wav\n", "03-02-06-01-01-02-11.wav\n", "03-02-05-02-02-02-11.wav\n", "03-02-05-02-01-02-11.wav\n", "03-02-05-02-02-01-11.wav\n", "03-02-06-01-02-02-11.wav\n", "03-02-06-01-02-01-11.wav\n", "03-02-06-02-01-01-11.wav\n", "03-02-06-02-02-01-11.wav\n", "03-02-06-02-01-02-11.wav\n", "03-02-06-02-02-02-11.wav\n", "03-01-02-01-01-01-06.wav\n", "03-01-02-01-01-02-06.wav\n", "03-01-01-01-02-01-06.wav\n", "03-01-02-01-02-01-06.wav\n", "03-01-01-01-02-02-06.wav\n", "03-01-01-01-01-01-06.wav\n", "03-01-01-01-01-02-06.wav\n", "03-01-02-02-01-01-06.wav\n", "03-01-02-02-01-02-06.wav\n", "03-01-03-01-01-01-06.wav\n", "03-01-02-02-02-02-06.wav\n", "03-01-02-02-02-01-06.wav\n", "03-01-03-01-01-02-06.wav\n", "03-01-03-01-02-01-06.wav\n", "03-01-03-01-02-02-06.wav\n", "03-01-03-02-01-01-06.wav\n", "03-01-03-02-01-02-06.wav\n", "03-01-03-02-02-02-06.wav\n", "03-01-03-02-02-01-06.wav\n", "03-01-04-01-01-01-06.wav\n", "03-01-04-01-01-02-06.wav\n", "03-01-04-01-02-01-06.wav\n", "03-01-04-02-01-01-06.wav\n", "03-01-04-01-02-02-06.wav\n", "03-01-04-02-01-02-06.wav\n", "03-01-05-01-01-02-06.wav\n", "03-01-04-02-02-02-06.wav\n", "03-01-05-01-01-01-06.wav\n", "03-01-05-01-02-01-06.wav\n", "03-01-05-01-02-02-06.wav\n", "03-01-04-02-02-01-06.wav\n", "03-01-05-02-01-01-06.wav\n", "03-01-06-01-01-02-06.wav\n", "03-01-05-02-01-02-06.wav\n", "03-01-06-01-01-01-06.wav\n", "03-01-05-02-02-01-06.wav\n", "03-01-05-02-02-02-06.wav\n", "03-01-06-01-02-01-06.wav\n", "03-01-06-02-01-01-06.wav\n", "03-01-06-01-02-02-06.wav\n", "03-01-06-02-02-02-06.wav\n", "03-01-06-02-02-01-06.wav\n", "03-01-06-02-01-02-06.wav\n", "03-01-07-01-01-01-06.wav\n", "03-01-07-01-01-02-06.wav\n", "03-01-07-01-02-01-06.wav\n", "03-01-07-02-01-01-06.wav\n", "03-01-07-02-01-02-06.wav\n", "03-01-07-01-02-02-06.wav\n", "03-01-07-02-02-01-06.wav\n", "03-01-07-02-02-02-06.wav\n", "03-01-08-01-01-02-06.wav\n", "03-01-08-01-02-02-06.wav\n", "03-01-08-01-01-01-06.wav\n", "03-01-08-01-02-01-06.wav\n", "03-01-08-02-01-01-06.wav\n", "03-01-08-02-01-02-06.wav\n", "03-01-08-02-02-01-06.wav\n", "03-01-08-02-02-02-06.wav\n", "03-02-01-01-01-02-06.wav\n", "03-02-01-01-01-01-06.wav\n", "03-02-01-01-02-01-06.wav\n", "03-02-01-01-02-02-06.wav\n", "03-02-02-01-01-01-06.wav\n", "03-02-02-01-01-02-06.wav\n", "03-02-02-01-02-02-06.wav\n", "03-02-02-01-02-01-06.wav\n", "03-02-02-02-01-01-06.wav\n", "03-02-02-02-02-01-06.wav\n", "03-02-02-02-01-02-06.wav\n", "03-02-02-02-02-02-06.wav\n", "03-02-03-01-01-02-06.wav\n", "03-02-03-01-01-01-06.wav\n", "03-02-03-01-02-01-06.wav\n", "03-02-03-01-02-02-06.wav\n", "03-02-04-01-01-01-06.wav\n", "03-02-04-01-01-02-06.wav\n", "03-02-03-02-01-01-06.wav\n", "03-02-03-02-01-02-06.wav\n", "03-02-04-01-02-01-06.wav\n", "03-02-03-02-02-01-06.wav\n", "03-02-04-01-02-02-06.wav\n", "03-02-03-02-02-02-06.wav\n", "03-01-02-01-02-02-06.wav\n", "03-02-05-01-01-01-06.wav\n", "03-02-04-02-01-02-06.wav\n", "03-02-05-01-01-02-06.wav\n", "03-02-04-02-02-01-06.wav\n", "03-02-05-01-02-01-06.wav\n", "03-02-04-02-01-01-06.wav\n", "03-02-04-02-02-02-06.wav\n", "03-02-05-01-02-02-06.wav\n", "03-02-05-02-01-01-06.wav\n", "03-02-05-02-01-02-06.wav\n", "03-02-05-02-02-01-06.wav\n", "03-02-06-01-02-01-06.wav\n", "03-02-06-01-01-02-06.wav\n", "03-02-06-02-01-01-06.wav\n", "03-02-06-02-02-01-06.wav\n", "03-02-06-01-02-02-06.wav\n", "03-02-06-02-01-02-06.wav\n", "03-02-05-02-02-02-06.wav\n", "03-02-06-01-01-01-06.wav\n", "03-02-06-02-02-02-06.wav\n", "03-01-01-01-01-01-12.wav\n", "03-01-01-01-01-02-12.wav\n", "03-01-01-01-02-01-12.wav\n", "03-01-02-01-01-01-12.wav\n", "03-01-01-01-02-02-12.wav\n", "03-01-02-01-02-01-12.wav\n", "03-01-02-01-01-02-12.wav\n", "03-01-02-01-02-02-12.wav\n", "03-01-02-02-01-01-12.wav\n", "03-01-02-02-01-02-12.wav\n", "03-01-02-02-02-01-12.wav\n", "03-01-02-02-02-02-12.wav\n", "03-01-03-01-01-01-12.wav\n", "03-01-03-01-02-01-12.wav\n", "03-01-03-01-01-02-12.wav\n", "03-01-03-01-02-02-12.wav\n", "03-01-03-02-01-01-12.wav\n", "03-01-03-02-02-01-12.wav\n", "03-01-03-02-01-02-12.wav\n", "03-01-04-01-01-01-12.wav\n", "03-01-04-01-02-01-12.wav\n", "03-01-04-01-01-02-12.wav\n", "03-01-04-01-02-02-12.wav\n", "03-01-03-02-02-02-12.wav\n", "03-01-04-02-01-01-12.wav\n", "03-01-04-02-01-02-12.wav\n", "03-01-05-01-01-01-12.wav\n", "03-01-04-02-02-01-12.wav\n", "03-01-05-02-01-01-12.wav\n", "03-01-04-02-02-02-12.wav\n", "03-01-05-02-01-02-12.wav\n", "03-01-05-01-01-02-12.wav\n", "03-01-05-02-02-01-12.wav\n", "03-01-05-01-02-01-12.wav\n", "03-01-05-02-02-02-12.wav\n", "03-01-05-01-02-02-12.wav\n", "03-01-06-01-01-01-12.wav\n", "03-01-06-01-01-02-12.wav\n", "03-01-06-02-01-01-12.wav\n", "03-01-06-01-02-01-12.wav\n", "03-01-06-02-01-02-12.wav\n", "03-01-06-01-02-02-12.wav\n", "03-01-07-01-01-01-12.wav\n", "03-01-06-02-02-01-12.wav\n", "03-01-06-02-02-02-12.wav\n", "03-01-07-01-01-02-12.wav\n", "03-01-07-01-02-01-12.wav\n", "03-01-07-02-01-01-12.wav\n", "03-01-07-01-02-02-12.wav\n", "03-01-07-02-02-01-12.wav\n", "03-01-07-02-01-02-12.wav\n", "03-01-08-01-01-01-12.wav\n", "03-01-07-02-02-02-12.wav\n", "03-01-08-01-02-01-12.wav\n", "03-01-08-01-01-02-12.wav\n", "03-01-08-02-01-01-12.wav\n", "03-01-08-01-02-02-12.wav\n", "03-01-08-02-02-01-12.wav\n", "03-01-08-02-01-02-12.wav\n", "03-01-08-02-02-02-12.wav\n", "03-02-01-01-01-01-12.wav\n", "03-02-01-01-02-01-12.wav\n", "03-02-01-01-01-02-12.wav\n", "03-02-02-01-02-01-12.wav\n", "03-02-02-01-01-01-12.wav\n", "03-02-01-01-02-02-12.wav\n", "03-02-02-01-02-02-12.wav\n", "03-02-02-02-01-01-12.wav\n", "03-02-02-02-01-02-12.wav\n", "03-02-02-01-01-02-12.wav\n", "03-02-03-02-01-01-12.wav\n", "03-02-02-02-02-01-12.wav\n", "03-02-02-02-02-02-12.wav\n", "03-02-03-02-01-02-12.wav\n", "03-02-03-01-01-01-12.wav\n", "03-02-03-01-02-01-12.wav\n", "03-02-03-01-02-02-12.wav\n", "03-02-03-01-01-02-12.wav\n", "03-02-03-02-02-01-12.wav\n", "03-02-03-02-02-02-12.wav\n", "03-02-04-01-01-01-12.wav\n", "03-02-04-02-02-01-12.wav\n", "03-02-04-01-01-02-12.wav\n", "03-02-04-02-02-02-12.wav\n", "03-02-04-01-02-01-12.wav\n", "03-02-05-01-01-01-12.wav\n", "03-02-04-01-02-02-12.wav\n", "03-02-05-01-01-02-12.wav\n", "03-02-04-02-01-01-12.wav\n", "03-02-04-02-01-02-12.wav\n", "03-02-05-01-02-01-12.wav\n", "03-02-05-01-02-02-12.wav\n", "03-02-05-02-01-01-12.wav\n", "03-02-06-01-01-01-12.wav\n", "03-02-06-01-01-02-12.wav\n", "03-02-05-02-02-01-12.wav\n", "03-02-05-02-01-02-12.wav\n", "03-02-05-02-02-02-12.wav\n", "03-02-06-01-02-01-12.wav\n", "03-02-06-01-02-02-12.wav\n", "03-02-06-02-01-01-12.wav\n", "03-02-06-02-02-01-12.wav\n", "03-02-06-02-01-02-12.wav\n", "03-02-06-02-02-02-12.wav\n", "03-01-01-01-01-02-13.wav\n", "03-01-01-01-01-01-13.wav\n", "03-01-01-01-02-01-13.wav\n", "03-01-02-02-02-02-13.wav\n", "03-01-01-01-02-02-13.wav\n", "03-01-02-02-01-01-13.wav\n", "03-01-02-01-02-02-13.wav\n", "03-01-02-01-01-01-13.wav\n", "03-01-02-02-02-01-13.wav\n", "03-01-02-02-01-02-13.wav\n", "03-01-02-01-01-02-13.wav\n", "03-01-02-01-02-01-13.wav\n", "03-01-03-01-01-01-13.wav\n", "03-01-03-02-02-02-13.wav\n", "03-01-03-01-02-02-13.wav\n", "03-01-03-01-02-01-13.wav\n", "03-01-03-01-01-02-13.wav\n", "03-01-04-01-01-02-13.wav\n", "03-01-03-02-01-02-13.wav\n", "03-01-03-02-02-01-13.wav\n", "03-01-03-02-01-01-13.wav\n", "03-01-04-01-01-01-13.wav\n", "03-01-04-01-02-01-13.wav\n", "03-01-04-01-02-02-13.wav\n", "03-01-04-02-02-01-13.wav\n", "03-01-04-02-01-01-13.wav\n", "03-01-05-01-01-01-13.wav\n", "03-01-05-01-01-02-13.wav\n", "03-01-04-02-02-02-13.wav\n", "03-01-04-02-01-02-13.wav\n", "03-01-05-01-02-02-13.wav\n", "03-01-05-01-02-01-13.wav\n", "03-01-05-02-01-01-13.wav\n", "03-01-05-02-02-01-13.wav\n", "03-01-06-01-01-01-13.wav\n", "03-01-05-02-01-02-13.wav\n", "03-01-05-02-02-02-13.wav\n", "03-01-06-01-01-02-13.wav\n", "03-01-06-01-02-02-13.wav\n", "03-01-06-02-01-01-13.wav\n", "03-01-06-01-02-01-13.wav\n", "03-01-06-02-02-01-13.wav\n", "03-01-06-02-01-02-13.wav\n", "03-01-06-02-02-02-13.wav\n", "03-01-07-01-01-01-13.wav\n", "03-01-07-01-01-02-13.wav\n", "03-01-07-01-02-01-13.wav\n", "03-01-08-01-01-01-13.wav\n", "03-01-07-02-01-01-13.wav\n", "03-01-07-01-02-02-13.wav\n", "03-01-08-01-02-01-13.wav\n", "03-01-08-01-01-02-13.wav\n", "03-01-07-02-02-01-13.wav\n", "03-01-07-02-01-02-13.wav\n", "03-01-07-02-02-02-13.wav\n", "03-01-08-01-02-02-13.wav\n", "03-01-08-02-01-01-13.wav\n", "03-01-08-02-01-02-13.wav\n", "03-02-01-01-02-01-13.wav\n", "03-01-08-02-02-01-13.wav\n", "03-01-08-02-02-02-13.wav\n", "03-02-01-01-02-02-13.wav\n", "03-02-02-01-01-01-13.wav\n", "03-02-01-01-01-01-13.wav\n", "03-02-01-01-01-02-13.wav\n", "03-02-02-01-01-02-13.wav\n", "03-02-02-01-02-01-13.wav\n", "03-02-03-01-01-01-13.wav\n", "03-02-02-01-02-02-13.wav\n", "03-02-03-01-01-02-13.wav\n", "03-02-02-02-01-01-13.wav\n", "03-02-03-01-02-02-13.wav\n", "03-02-03-01-02-01-13.wav\n", "03-02-02-02-01-02-13.wav\n", "03-02-02-02-02-01-13.wav\n", "03-02-02-02-02-02-13.wav\n", "03-02-03-02-01-01-13.wav\n", "03-02-03-02-01-02-13.wav\n", "03-02-03-02-02-02-13.wav\n", "03-02-04-01-01-01-13.wav\n", "03-02-04-02-01-01-13.wav\n", "03-02-04-01-02-01-13.wav\n", "03-02-04-01-01-02-13.wav\n", "03-02-04-01-02-02-13.wav\n", "03-02-04-02-01-02-13.wav\n", "03-02-03-02-02-01-13.wav\n", "03-02-04-02-02-01-13.wav\n", "03-02-05-02-01-01-13.wav\n", "03-02-04-02-02-02-13.wav\n", "03-02-05-02-02-01-13.wav\n", "03-02-05-01-01-01-13.wav\n", "03-02-05-01-01-02-13.wav\n", "03-02-05-02-01-02-13.wav\n", "03-02-05-02-02-02-13.wav\n", "03-02-05-01-02-02-13.wav\n", "03-02-05-01-02-01-13.wav\n", "03-02-06-01-01-01-13.wav\n", "03-02-06-01-01-02-13.wav\n", "03-02-06-02-01-01-13.wav\n", "03-02-06-01-02-01-13.wav\n", "03-02-06-02-01-02-13.wav\n", "03-02-06-02-02-01-13.wav\n", "03-02-06-01-02-02-13.wav\n", "03-02-06-02-02-02-13.wav\n", "03-01-02-01-01-02-02.wav\n", "03-01-01-01-01-01-02.wav\n", "03-01-02-01-02-01-02.wav\n", "03-01-01-01-01-02-02.wav\n", "03-01-02-01-02-02-02.wav\n", "03-01-01-01-02-01-02.wav\n", "03-01-02-02-01-01-02.wav\n", "03-01-02-01-01-01-02.wav\n", "03-01-01-01-02-02-02.wav\n", "03-01-02-02-01-02-02.wav\n", "03-01-03-01-01-02-02.wav\n", "03-01-02-02-02-01-02.wav\n", "03-01-03-01-02-01-02.wav\n", "03-01-02-02-02-02-02.wav\n", "03-01-03-01-01-01-02.wav\n", "03-01-03-02-01-02-02.wav\n", "03-01-03-01-02-02-02.wav\n", "03-01-03-02-01-01-02.wav\n", "03-01-03-02-02-01-02.wav\n", "03-01-03-02-02-02-02.wav\n", "03-01-04-01-01-01-02.wav\n", "03-01-04-01-01-02-02.wav\n", "03-01-04-01-02-01-02.wav\n", "03-01-04-01-02-02-02.wav\n", "03-01-04-02-01-02-02.wav\n", "03-01-04-02-01-01-02.wav\n", "03-01-04-02-02-01-02.wav\n", "03-01-05-01-01-01-02.wav\n", "03-01-05-01-01-02-02.wav\n", "03-01-04-02-02-02-02.wav\n", "03-01-05-01-02-01-02.wav\n", "03-01-05-01-02-02-02.wav\n", "03-01-05-02-01-01-02.wav\n", "03-01-05-02-01-02-02.wav\n", "03-01-05-02-02-01-02.wav\n", "03-01-05-02-02-02-02.wav\n", "03-01-06-01-02-01-02.wav\n", "03-01-06-01-01-02-02.wav\n", "03-01-06-01-01-01-02.wav\n", "03-01-06-01-02-02-02.wav\n", "03-01-06-02-01-01-02.wav\n", "03-01-06-02-01-02-02.wav\n", "03-01-07-01-01-01-02.wav\n", "03-01-06-02-02-01-02.wav\n", "03-01-06-02-02-02-02.wav\n", "03-01-07-01-01-02-02.wav\n", "03-01-07-01-02-01-02.wav\n", "03-01-07-02-02-02-02.wav\n", "03-01-07-01-02-02-02.wav\n", "03-01-08-01-01-01-02.wav\n", "03-01-07-02-01-01-02.wav\n", "03-01-08-01-01-02-02.wav\n", "03-01-07-02-01-02-02.wav\n", "03-01-08-01-02-01-02.wav\n", "03-01-07-02-02-01-02.wav\n", "03-01-08-01-02-02-02.wav\n", "03-01-08-02-02-02-02.wav\n", "03-01-08-02-01-01-02.wav\n", "03-02-01-01-01-01-02.wav\n", "03-01-08-02-01-02-02.wav\n", "03-02-01-01-01-02-02.wav\n", "03-01-08-02-02-01-02.wav\n", "03-02-01-01-02-01-02.wav\n", "03-02-02-01-01-01-02.wav\n", "03-02-01-01-02-02-02.wav\n", "03-02-02-01-01-02-02.wav\n", "03-02-02-01-02-02-02.wav\n", "03-02-02-01-02-01-02.wav\n", "03-02-02-02-01-02-02.wav\n", "03-02-02-02-01-01-02.wav\n", "03-02-02-02-02-01-02.wav\n", "03-02-03-01-01-01-02.wav\n", "03-02-02-02-02-02-02.wav\n", "03-02-03-01-01-02-02.wav\n", "03-02-03-01-02-01-02.wav\n", "03-02-03-01-02-02-02.wav\n", "03-02-03-02-01-01-02.wav\n", "03-02-03-02-01-02-02.wav\n", "03-02-03-02-02-01-02.wav\n", "03-02-03-02-02-02-02.wav\n", "03-02-04-01-02-02-02.wav\n", "03-02-04-01-01-02-02.wav\n", "03-02-04-01-02-01-02.wav\n", "03-02-04-01-01-01-02.wav\n", "03-02-04-02-01-02-02.wav\n", "03-02-04-02-01-01-02.wav\n", "03-02-04-02-02-02-02.wav\n", "03-02-04-02-02-01-02.wav\n", "03-02-05-01-01-01-02.wav\n", "03-02-05-01-01-02-02.wav\n", "03-02-05-01-02-02-02.wav\n", "03-02-05-01-02-01-02.wav\n", "03-02-05-02-02-02-02.wav\n", "03-02-05-02-01-01-02.wav\n", "03-02-05-02-02-01-02.wav\n", "03-02-06-01-01-01-02.wav\n", "03-02-05-02-01-02-02.wav\n", "03-02-06-01-02-01-02.wav\n", "03-02-06-01-01-02-02.wav\n", "03-02-06-01-02-02-02.wav\n", "03-02-06-02-01-01-02.wav\n", "03-02-06-02-01-02-02.wav\n", "03-02-06-02-02-01-02.wav\n", "03-02-06-02-02-02-02.wav\n", "03-01-01-01-01-02-01.wav\n", "03-01-01-01-02-01-01.wav\n", "03-01-01-01-02-02-01.wav\n", "03-01-01-01-01-01-01.wav\n", "03-01-02-01-01-01-01.wav\n", "03-01-02-01-02-01-01.wav\n", "03-01-02-01-02-02-01.wav\n", "03-01-02-02-01-02-01.wav\n", "03-01-02-02-01-01-01.wav\n", "03-01-03-01-01-02-01.wav\n", "03-01-02-01-01-02-01.wav\n", "03-01-02-02-02-01-01.wav\n", "03-01-02-02-02-02-01.wav\n", "03-01-03-01-02-01-01.wav\n", "03-01-03-01-01-01-01.wav\n", "03-01-03-01-02-02-01.wav\n", "03-01-03-02-01-02-01.wav\n", "03-01-03-02-01-01-01.wav\n", "03-01-03-02-02-02-01.wav\n", "03-01-03-02-02-01-01.wav\n", "03-01-04-01-01-02-01.wav\n", "03-01-04-01-01-01-01.wav\n", "03-01-04-01-02-02-01.wav\n", "03-01-04-01-02-01-01.wav\n", "03-01-04-02-01-02-01.wav\n", "03-01-04-02-01-01-01.wav\n", "03-01-04-02-02-02-01.wav\n", "03-01-04-02-02-01-01.wav\n", "03-01-05-01-01-01-01.wav\n", "03-01-05-01-01-02-01.wav\n", "03-01-05-01-02-02-01.wav\n", "03-01-05-01-02-01-01.wav\n", "03-01-06-01-01-01-01.wav\n", "03-01-05-02-01-01-01.wav\n", "03-01-05-02-01-02-01.wav\n", "03-01-05-02-02-01-01.wav\n", "03-01-06-01-01-02-01.wav\n", "03-01-06-01-02-01-01.wav\n", "03-01-05-02-02-02-01.wav\n", "03-01-06-01-02-02-01.wav\n", "03-01-07-01-01-02-01.wav\n", "03-01-06-02-01-01-01.wav\n", "03-01-07-01-02-01-01.wav\n", "03-01-06-02-01-02-01.wav\n", "03-01-07-01-02-02-01.wav\n", "03-01-06-02-02-01-01.wav\n", "03-01-06-02-02-02-01.wav\n", "03-01-07-01-01-01-01.wav\n", "03-01-07-02-01-01-01.wav\n", "03-01-07-02-01-02-01.wav\n", "03-01-08-02-01-01-01.wav\n", "03-01-07-02-02-01-01.wav\n", "03-01-08-02-02-01-01.wav\n", "03-01-08-02-01-02-01.wav\n", "03-01-07-02-02-02-01.wav\n", "03-01-08-01-01-02-01.wav\n", "03-01-08-01-02-02-01.wav\n", "03-01-08-01-01-01-01.wav\n", "03-01-08-01-02-01-01.wav\n", "03-02-01-01-02-01-01.wav\n", "03-02-01-01-01-01-01.wav\n", "03-01-08-02-02-02-01.wav\n", "03-02-01-01-02-02-01.wav\n", "03-02-02-01-01-01-01.wav\n", "03-02-02-01-02-02-01.wav\n", "03-02-01-01-01-02-01.wav\n", "03-02-02-01-01-02-01.wav\n", "03-02-02-02-01-01-01.wav\n", "03-02-02-01-02-01-01.wav\n", "03-02-02-02-01-02-01.wav\n", "03-02-02-02-02-01-01.wav\n", "03-02-02-02-02-02-01.wav\n", "03-02-03-01-01-02-01.wav\n", "03-02-03-01-01-01-01.wav\n", "03-02-03-01-02-02-01.wav\n", "03-02-03-01-02-01-01.wav\n", "03-02-03-02-02-01-01.wav\n", "03-02-03-02-01-02-01.wav\n", "03-02-03-02-01-01-01.wav\n", "03-02-03-02-02-02-01.wav\n", "03-02-04-01-01-01-01.wav\n", "03-02-04-01-01-02-01.wav\n", "03-02-04-01-02-01-01.wav\n", "03-02-04-02-01-02-01.wav\n", "03-02-04-02-02-01-01.wav\n", "03-02-05-01-01-02-01.wav\n", "03-02-05-01-01-01-01.wav\n", "03-02-04-01-02-02-01.wav\n", "03-02-05-01-02-02-01.wav\n", "03-02-05-01-02-01-01.wav\n", "03-02-04-02-02-02-01.wav\n", "03-02-04-02-01-01-01.wav\n", "03-02-05-02-01-01-01.wav\n", "03-02-05-02-01-02-01.wav\n", "03-02-06-02-02-01-01.wav\n", "03-02-05-02-02-01-01.wav\n", "03-02-06-01-02-02-01.wav\n", "03-02-06-02-01-01-01.wav\n", "03-02-05-02-02-02-01.wav\n", "03-02-06-02-01-02-01.wav\n", "03-02-06-01-01-02-01.wav\n", "03-02-06-01-01-01-01.wav\n", "03-02-06-01-02-01-01.wav\n", "03-02-06-02-02-02-01.wav\n", "03-01-01-01-02-02-04.wav\n", "03-01-02-01-01-01-04.wav\n", "03-01-01-01-01-01-04.wav\n", "03-01-02-01-01-02-04.wav\n", "03-01-01-01-02-01-04.wav\n", "03-01-02-01-02-01-04.wav\n", "03-01-01-01-01-02-04.wav\n", "03-01-02-01-02-02-04.wav\n", "03-01-02-02-01-01-04.wav\n", "03-01-02-02-01-02-04.wav\n", "03-01-03-01-01-02-04.wav\n", "03-01-02-02-02-01-04.wav\n", "03-01-03-01-02-01-04.wav\n", "03-01-02-02-02-02-04.wav\n", "03-01-03-01-02-02-04.wav\n", "03-01-03-01-01-01-04.wav\n", "03-01-03-02-01-01-04.wav\n", "03-01-03-02-01-02-04.wav\n", "03-01-04-01-02-02-04.wav\n", "03-01-03-02-02-01-04.wav\n", "03-01-04-02-01-01-04.wav\n", "03-01-03-02-02-02-04.wav\n", "03-01-04-02-01-02-04.wav\n", "03-01-04-01-01-02-04.wav\n", "03-01-04-02-02-01-04.wav\n", "03-01-04-01-01-01-04.wav\n", "03-01-04-01-02-01-04.wav\n", "03-01-04-02-02-02-04.wav\n", "03-01-05-01-01-01-04.wav\n", "03-01-05-01-01-02-04.wav\n", "03-01-05-01-02-02-04.wav\n", "03-01-05-01-02-01-04.wav\n", "03-01-05-02-01-01-04.wav\n", "03-01-05-02-01-02-04.wav\n", "03-01-05-02-02-02-04.wav\n", "03-01-06-01-01-01-04.wav\n", "03-01-05-02-02-01-04.wav\n", "03-01-06-01-01-02-04.wav\n", "03-01-06-01-02-02-04.wav\n", "03-01-06-01-02-01-04.wav\n", "03-01-06-02-01-01-04.wav\n", "03-01-06-02-01-02-04.wav\n", "03-01-06-02-02-01-04.wav\n", "03-01-06-02-02-02-04.wav\n", "03-01-07-01-02-02-04.wav\n", "03-01-07-01-01-02-04.wav\n", "03-01-07-01-01-01-04.wav\n", "03-01-07-01-02-01-04.wav\n", "03-01-07-02-01-01-04.wav\n", "03-01-07-02-01-02-04.wav\n", "03-01-07-02-02-01-04.wav\n", "03-01-07-02-02-02-04.wav\n", "03-01-08-01-01-01-04.wav\n", "03-01-08-01-01-02-04.wav\n", "03-01-08-02-01-02-04.wav\n", "03-01-08-02-02-01-04.wav\n", "03-01-08-01-02-01-04.wav\n", "03-01-08-02-01-01-04.wav\n", "03-01-08-01-02-02-04.wav\n", "03-02-01-01-01-01-04.wav\n", "03-01-08-02-02-02-04.wav\n", "03-02-01-01-01-02-04.wav\n", "03-02-02-01-02-02-04.wav\n", "03-02-01-01-02-01-04.wav\n", "03-02-02-02-01-01-04.wav\n", "03-02-01-01-02-02-04.wav\n", "03-02-02-01-01-02-04.wav\n", "03-02-02-01-01-01-04.wav\n", "03-02-02-01-02-01-04.wav\n", "03-02-02-02-01-02-04.wav\n", "03-02-02-02-02-01-04.wav\n", "03-02-02-02-02-02-04.wav\n", "03-02-03-02-01-02-04.wav\n", "03-02-03-01-01-01-04.wav\n", "03-02-03-02-02-01-04.wav\n", "03-02-03-01-01-02-04.wav\n", "03-02-03-02-02-02-04.wav\n", "03-02-03-01-02-01-04.wav\n", "03-02-04-01-01-01-04.wav\n", "03-02-03-01-02-02-04.wav\n", "03-02-03-02-01-01-04.wav\n", "03-02-04-01-01-02-04.wav\n", "03-02-04-02-01-02-04.wav\n", "03-02-04-01-02-01-04.wav\n", "03-02-04-02-02-01-04.wav\n", "03-02-04-01-02-02-04.wav\n", "03-02-04-02-02-02-04.wav\n", "03-02-04-02-01-01-04.wav\n", "03-02-05-01-01-01-04.wav\n", "03-02-05-01-01-02-04.wav\n", "03-02-05-01-02-01-04.wav\n", "03-02-05-01-02-02-04.wav\n", "03-02-05-02-01-01-04.wav\n", "03-02-05-02-01-02-04.wav\n", "03-02-06-01-01-01-04.wav\n", "03-02-05-02-02-01-04.wav\n", "03-02-05-02-02-02-04.wav\n", "03-02-06-01-01-02-04.wav\n", "03-02-06-01-02-01-04.wav\n", "03-02-06-02-01-02-04.wav\n", "03-02-06-01-02-02-04.wav\n", "03-02-06-02-02-01-04.wav\n", "03-02-06-02-01-01-04.wav\n", "03-02-06-02-02-02-04.wav\n", "03-01-01-01-01-01-03.wav\n", "03-01-01-01-01-02-03.wav\n", "03-01-01-01-02-01-03.wav\n", "03-01-01-01-02-02-03.wav\n", "03-01-02-01-02-02-03.wav\n", "03-01-02-01-01-01-03.wav\n", "03-01-02-02-01-01-03.wav\n", "03-01-02-02-01-02-03.wav\n", "03-01-02-01-01-02-03.wav\n", "03-01-02-02-02-01-03.wav\n", "03-01-02-01-02-01-03.wav\n", "03-01-02-02-02-02-03.wav\n", "03-01-03-01-02-02-03.wav\n", "03-01-03-01-01-01-03.wav\n", "03-01-03-02-01-01-03.wav\n", "03-01-03-01-01-02-03.wav\n", "03-01-03-02-02-01-03.wav\n", "03-01-03-01-02-01-03.wav\n", "03-01-03-02-02-02-03.wav\n", "03-01-03-02-01-02-03.wav\n", "03-01-04-01-01-01-03.wav\n", "03-01-04-01-01-02-03.wav\n", "03-01-04-02-01-01-03.wav\n", "03-01-04-01-02-02-03.wav\n", "03-01-04-01-02-01-03.wav\n", "03-01-04-02-01-02-03.wav\n", "03-01-04-02-02-01-03.wav\n", "03-01-04-02-02-02-03.wav\n", "03-01-05-01-01-01-03.wav\n", "03-01-05-01-02-02-03.wav\n", "03-01-05-01-01-02-03.wav\n", "03-01-05-02-01-01-03.wav\n", "03-01-05-01-02-01-03.wav\n", "03-01-05-02-01-02-03.wav\n", "03-01-05-02-02-01-03.wav\n", "03-01-06-01-01-01-03.wav\n", "03-01-06-02-01-02-03.wav\n", "03-01-06-01-01-02-03.wav\n", "03-01-06-02-02-01-03.wav\n", "03-01-06-01-02-02-03.wav\n", "03-01-06-02-02-02-03.wav\n", "03-01-05-02-02-02-03.wav\n", "03-01-07-01-01-01-03.wav\n", "03-01-06-01-02-01-03.wav\n", "03-01-06-02-01-01-03.wav\n", "03-01-07-01-01-02-03.wav\n", "03-01-07-01-02-01-03.wav\n", "03-01-07-02-01-02-03.wav\n", "03-01-08-01-01-02-03.wav\n", "03-01-07-01-02-02-03.wav\n", "03-01-08-01-02-01-03.wav\n", "03-01-07-02-01-01-03.wav\n", "03-01-07-02-02-01-03.wav\n", "03-01-07-02-02-02-03.wav\n", "03-01-08-01-01-01-03.wav\n", "03-01-08-01-02-02-03.wav\n", "03-01-08-02-01-01-03.wav\n", "03-01-08-02-01-02-03.wav\n", "03-01-08-02-02-02-03.wav\n", "03-01-08-02-02-01-03.wav\n", "03-02-01-01-01-01-03.wav\n", "03-02-01-01-01-02-03.wav\n", "03-02-01-01-02-02-03.wav\n", "03-02-02-01-02-01-03.wav\n", "03-02-01-01-02-01-03.wav\n", "03-02-02-01-01-02-03.wav\n", "03-02-02-01-01-01-03.wav\n", "03-02-02-01-02-02-03.wav\n", "03-02-02-02-01-01-03.wav\n", "03-02-02-02-01-02-03.wav\n", "03-02-02-02-02-01-03.wav\n", "03-02-03-01-02-02-03.wav\n", "03-02-02-02-02-02-03.wav\n", "03-02-03-02-01-01-03.wav\n", "03-02-03-01-01-01-03.wav\n", "03-02-03-02-01-02-03.wav\n", "03-02-03-01-02-01-03.wav\n", "03-02-03-02-02-01-03.wav\n", "03-02-03-01-01-02-03.wav\n", "03-02-03-02-02-02-03.wav\n", "03-02-04-01-01-01-03.wav\n", "03-02-04-01-02-02-03.wav\n", "03-02-04-01-01-02-03.wav\n", "03-02-04-02-01-01-03.wav\n", "03-02-04-01-02-01-03.wav\n", "03-02-04-02-01-02-03.wav\n", "03-02-04-02-02-01-03.wav\n", "03-02-04-02-02-02-03.wav\n", "03-02-05-01-01-02-03.wav\n", "03-02-05-01-01-01-03.wav\n", "03-02-05-02-01-01-03.wav\n", "03-02-05-01-02-01-03.wav\n", "03-02-05-01-02-02-03.wav\n", "03-02-05-02-01-02-03.wav\n", "03-02-05-02-02-01-03.wav\n", "03-02-05-02-02-02-03.wav\n", "03-02-06-01-02-02-03.wav\n", "03-02-06-01-01-01-03.wav\n", "03-02-06-02-01-01-03.wav\n", "03-02-06-01-01-02-03.wav\n", "03-02-06-01-02-01-03.wav\n", "03-02-06-02-01-02-03.wav\n", "03-02-06-02-02-01-03.wav\n", "03-02-06-02-02-02-03.wav\n", "[+] Number of training samples: 985\n", "[+] Number of testing samples: 329\n", "[+] Number of features: 180\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "j-2WVGMZDPGu", "colab_type": "text"}, "source": ["**DECISION TREE**"]}, {"cell_type": "code", "metadata": {"id": "TJSh1g49ALTV", "colab_type": "code", "outputId": "b564fb35-a537-403a-915c-ebf7c0ccdbb4", "colab": {"base_uri": "https://localhost:8080/", "height": 289}}, "source": ["from sklearn.tree import DecisionTreeClassifier \n", "from sklearn.metrics import accuracy_score\n", "from sklearn.metrics import classification_report\n", "from sklearn.metrics import confusion_matrix\n", "\n", "\n", "dtree_model = DecisionTreeClassifier(max_depth = 2).fit(X_train, y_train) \n", "dtree_predictions = dtree_model.predict(X_test) \n", "\n", "print(accuracy_score(y_true=y_test,y_pred=dtree_predictions))\n", "print(classification_report(y_test,dtree_predictions)) \n", "# creating a confusion matrix \n", "print(confusion_matrix(y_test, dtree_predictions) )"], "execution_count": 0, "outputs": [{"output_type": "stream", "text": ["0.5501519756838906\n", "              precision    recall  f1-score   support\n", "\n", "       angry       0.88      0.47      0.61        90\n", "       happy       0.49      0.66      0.56        94\n", "     neutral       0.28      0.27      0.28        44\n", "         sad       0.59      0.64      0.61       101\n", "\n", "    accuracy                           0.55       329\n", "   macro avg       0.56      0.51      0.51       329\n", "weighted avg       0.60      0.55      0.55       329\n", "\n", "[[42 34 11  3]\n", " [ 6 62  9 17]\n", " [ 0  6 12 26]\n", " [ 0 25 11 65]]\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "jo_-cYFVDMqB", "colab_type": "code", "outputId": "80db6b34-3a64-410f-cb19-facf4e030351", "colab": {"base_uri": "https://localhost:8080/", "height": 289}}, "source": ["from sklearn.tree import DecisionTreeClassifier \n", "from sklearn.metrics import accuracy_score\n", "from sklearn.metrics import classification_report\n", "from sklearn.metrics import confusion_matrix\n", "\n", "\n", "dtree_model = DecisionTreeClassifier(max_depth = 6).fit(X_train, y_train) \n", "dtree_predictions = dtree_model.predict(X_test) \n", "\n", "print(accuracy_score(y_true=y_test,y_pred=dtree_predictions))\n", "print(classification_report(y_test,dtree_predictions)) \n", "# creating a confusion matrix \n", "print(confusion_matrix(y_test, dtree_predictions) )"], "execution_count": 0, "outputs": [{"output_type": "stream", "text": ["0.6595744680851063\n", "              precision    recall  f1-score   support\n", "\n", "       angry       0.74      0.74      0.74        90\n", "       happy       0.57      0.53      0.55        94\n", "     neutral       0.58      0.68      0.62        44\n", "         sad       0.71      0.69      0.70       101\n", "\n", "    accuracy                           0.66       329\n", "   macro avg       0.65      0.66      0.65       329\n", "weighted avg       0.66      0.66      0.66       329\n", "\n", "[[67 21  0  2]\n", " [17 50  8 19]\n", " [ 4  3 30  7]\n", " [ 3 14 14 70]]\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "Y2domdsBGPWd", "colab_type": "text"}, "source": ["DT"]}, {"cell_type": "code", "metadata": {"id": "rr-ksNHtEHfe", "colab_type": "code", "outputId": "e54ef6c4-f642-413f-8d08-9c5b709fae3f", "colab": {"base_uri": "https://localhost:8080/", "height": 289}}, "source": ["from sklearn.tree import DecisionTreeClassifier \n", "from sklearn.metrics import accuracy_score\n", "from sklearn.metrics import classification_report\n", "from sklearn.metrics import confusion_matrix\n", "\n", "\n", "dtree_model = DecisionTreeClassifier(max_depth = 9,random_state=0).fit(X_train, y_train) \n", "dtree_predictions = dtree_model.predict(X_test) \n", "\n", "print(accuracy_score(y_true=y_test,y_pred=dtree_predictions))\n", "print(classification_report(y_test,dtree_predictions)) \n", "# creating a confusion matrix \n", "print(confusion_matrix(y_test, dtree_predictions) )"], "execution_count": 0, "outputs": [{"output_type": "stream", "text": ["0.6656534954407295\n", "              precision    recall  f1-score   support\n", "\n", "       angry       0.66      0.76      0.70        90\n", "       happy       0.63      0.55      0.59        94\n", "     neutral       0.67      0.64      0.65        44\n", "         sad       0.70      0.70      0.70       101\n", "\n", "    accuracy                           0.67       329\n", "   macro avg       0.66      0.66      0.66       329\n", "weighted avg       0.66      0.67      0.66       329\n", "\n", "[[68 16  0  6]\n", " [22 52  3 17]\n", " [ 4  4 28  8]\n", " [ 9 10 11 71]]\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "64KjFNzqDqd-", "colab_type": "text"}, "source": ["**SUPPORT VECTOR MACHINE**"]}, {"cell_type": "code", "metadata": {"id": "81nRiRWwDxo2", "colab_type": "code", "outputId": "cd39a985-8b31-47cb-db9d-09575ebcbb91", "colab": {"base_uri": "https://localhost:8080/", "height": 289}}, "source": ["from sklearn.svm import SVC \n", "svm_model_linear = SVC(kernel = 'linear', C = 1).fit(X_train, y_train) \n", "svm_predictions = svm_model_linear.predict(X_test) \n", "\n", "\n", "print(accuracy_score(y_true=y_test,y_pred=svm_predictions))\n", "print(classification_report(y_test,svm_predictions)) \n", "# creating a confusion matrix \n", "print(confusion_matrix(y_test, svm_predictions) )"], "execution_count": 0, "outputs": [{"output_type": "stream", "text": ["0.7507598784194529\n", "              precision    recall  f1-score   support\n", "\n", "       angry       0.78      0.79      0.78        90\n", "       happy       0.73      0.70      0.71        94\n", "     neutral       0.65      0.73      0.69        44\n", "         sad       0.80      0.77      0.78       101\n", "\n", "    accuracy                           0.75       329\n", "   macro avg       0.74      0.75      0.74       329\n", "weighted avg       0.75      0.75      0.75       329\n", "\n", "[[71 13  4  2]\n", " [12 66  4 12]\n", " [ 3  3 32  6]\n", " [ 5  9  9 78]]\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "A_oElZsGEwkG", "colab_type": "code", "outputId": "ded15a68-2a37-4f6b-e016-435259c57972", "colab": {"base_uri": "https://localhost:8080/", "height": 343}}, "source": ["from sklearn.svm import SVC \n", "svm_model_linear = SVC().fit(X_train, y_train) \n", "svm_predictions = svm_model_linear.predict(X_test) \n", "\n", "\n", "print(accuracy_score(y_true=y_test,y_pred=svm_predictions))\n", "print(classification_report(y_test,svm_predictions)) \n", "# creating a confusion matrix \n", "print(confusion_matrix(y_test, svm_predictions) )"], "execution_count": 0, "outputs": [{"output_type": "stream", "text": ["0.48632218844984804\n", "              precision    recall  f1-score   support\n", "\n", "       angry       0.69      0.66      0.67        90\n", "       happy       0.36      0.40      0.38        94\n", "     neutral       0.00      0.00      0.00        44\n", "         sad       0.46      0.62      0.53       101\n", "\n", "    accuracy                           0.49       329\n", "   macro avg       0.38      0.42      0.39       329\n", "weighted avg       0.43      0.49      0.45       329\n", "\n", "[[59 27  0  4]\n", " [24 38  0 32]\n", " [ 1  5  0 38]\n", " [ 2 36  0 63]]\n"], "name": "stdout"}, {"output_type": "stream", "text": ["/usr/local/lib/python3.6/dist-packages/sklearn/metrics/_classification.py:1272: UndefinedMetricWarning: Precision and F-score are ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, msg_start, len(result))\n"], "name": "stderr"}]}, {"cell_type": "markdown", "metadata": {"id": "0kfaciZaHcp2", "colab_type": "text"}, "source": ["**RANDOM FOREST**"]}, {"cell_type": "code", "metadata": {"id": "VjB-aXmmHAN3", "colab_type": "code", "outputId": "c32564b3-b384-4110-bf6b-7a94e86b070e", "colab": {"base_uri": "https://localhost:8080/", "height": 289}}, "source": ["from sklearn.ensemble import RandomForestClassifier\n", "  \n", " # create regressor object \n", "classifier = RandomForestClassifier(n_estimators = 100, random_state = 0) \n", "  \n", "# fit the regressor with x and y data \n", "classifier.fit(X_train, y_train)   \n", "\n", "c_p = classifier.predict(X_test) \n", "\n", "\n", "print(accuracy_score(y_true=y_test,y_pred=c_p))\n", "print(classification_report(y_test,c_p)) \n", "# creating a confusion matrix \n", "print(confusion_matrix(y_test,c_p) )"], "execution_count": 0, "outputs": [{"output_type": "stream", "text": ["0.7659574468085106\n", "              precision    recall  f1-score   support\n", "\n", "       angry       0.87      0.87      0.87        90\n", "       happy       0.70      0.72      0.71        94\n", "     neutral       0.88      0.64      0.74        44\n", "         sad       0.71      0.77      0.74       101\n", "\n", "    accuracy                           0.77       329\n", "   macro avg       0.79      0.75      0.76       329\n", "weighted avg       0.77      0.77      0.77       329\n", "\n", "[[78 10  0  2]\n", " [ 9 68  1 16]\n", " [ 0  2 28 14]\n", " [ 3 17  3 78]]\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "qiozFUzAIdCW", "colab_type": "code", "outputId": "47518f76-73a9-43d7-b6a8-2af330dd6fab", "colab": {"base_uri": "https://localhost:8080/", "height": 289}}, "source": ["from sklearn.ensemble import RandomForestClassifier\n", "  \n", " # create regressor object \n", "classifier = RandomForestClassifier(n_estimators = 20,random_state = 0) \n", "  \n", "# fit the regressor with x and y data \n", "classifier.fit(X_train, y_train)   \n", "\n", "c_p = classifier.predict(X_test) \n", "\n", "\n", "print(accuracy_score(y_true=y_test,y_pred=c_p))\n", "print(classification_report(y_test,c_p)) \n", "# creating a confusion matrix \n", "print(confusion_matrix(y_test,c_p) )"], "execution_count": 0, "outputs": [{"output_type": "stream", "text": ["0.7142857142857143\n", "              precision    recall  f1-score   support\n", "\n", "       angry       0.79      0.86      0.82        90\n", "       happy       0.68      0.61      0.64        94\n", "     neutral       0.72      0.59      0.65        44\n", "         sad       0.68      0.74      0.71       101\n", "\n", "    accuracy                           0.71       329\n", "   macro avg       0.72      0.70      0.70       329\n", "weighted avg       0.71      0.71      0.71       329\n", "\n", "[[77 10  0  3]\n", " [14 57  4 19]\n", " [ 0  4 26 14]\n", " [ 7 13  6 75]]\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "xXzMm5uoKZJN", "colab_type": "code", "colab": {}}, "source": [""], "execution_count": 0, "outputs": []}]}